# 项目上下文信息

- 創建了 services/email_notification.go 電郵通知服務，包含13種電郵通知功能：1.新機構申請通知(admin) 2.協議準備完成通知(facility) 3.協議續期提醒(admin) 4.協議過期通知(facility) 5.機構資料審核通過(facility) 6.機構資料審核拒絕(facility) 7.專業人士資料審核拒絕(professional) 8.專業人士資料審核通過(professional) 9.發票通知(facility) 10.協助工單通知(admin) 11.工作未填滿緊急通知(facility) 12.專業人士取消工作通知(facility) 13.機構取消工作通知(professional)。每個通知都有對應的Req結構體和發送函數，使用i18n國際化，xmail.SendMail發送，hermes生成HTML內容。
- 完成了電郵通知服務的剩餘5個批量函數實現：1.新增AdminReminderInfo和AgreementReminderInfo兩個新的資料結構 2.實現SendNewFacilityApplicationEmailBatch()新機構申請批量通知 3.實現SendHelpDeskNotificationEmailBatch()協助工單批量通知 4.實現SendAgreementReadyEmailBatch()協議準備完成批量通知 5.實現SendAgreementRenewalReminderEmailBatch()協議續期提醒批量通知 6.實現SendAgreementExpiredEmailBatch()協議過期批量通知。現在所有13個電郵類型都有對應的批量發送函數，電郵通知服務達到100%完成度。
- Personal Accident & Illness Insurance feature added to Professional model: 1) Added constants ProfessionalPersonalAccidentIllnessInsuranceY/N and ProfessionalPersonalAccidentIllnessInsuranceAcknowledgmentY/N in professional.go; 2) Added fields HasPersonalAccidentIllnessInsurance and PersonalAccidentIllnessInsuranceAcknowledgment to ProfessionalProfile struct; 3) Added file type ProfessionalFileCodePersonalAccidentIllnessInsuranceCertificate in professional_file.go with proper mapping, size limits and allowed file types.
