# 常用模式和最佳实践

- Email notification batch function pattern: SendNewFacilityApplicationEmailBatch accepts facilityId and lang, sends to SendEmailTask, ProcessNewFacilityApplication queries all SYSTEM_ADMIN users and sends individual notifications. Function comments should be in Chinese without repeating function names, e.g., "// 處理新機構申請通知任務" not "// ProcessNewFacilityApplication 處理新機構申請通知任務".
- 新增 CreateProfessionalProfileNeedPaymentDetails 通知功能：在 model/system_notification.go 添加 PROFESSIONAL_PROFILE_NEED_PAYMENT_DETAILS 常量，在 services/system_notification.go 添加對應的文字常量和函數，用於 Profile 審核通過後提示完善 Payment Details 資料。
