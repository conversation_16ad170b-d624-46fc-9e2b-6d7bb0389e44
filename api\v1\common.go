package v1

import (
	"errors"
	"fmt"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xredis"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gorm.io/gorm"
)

const (
	AuthenticationRegisterRecordExpired      = 20003 // 註冊記錄已過期
	AuthenticationRegisterVerifyCodeExpired  = 20004 // 註冊驗證碼已過期
	AuthenticationRegisterCodeSendCountLimit = 20005 // 註冊驗證碼發送次數限制
	AuthenticationEmailAlreadyRegistered     = 20006 // 電郵已註冊
)

type CommonController struct{}

type ReqCommonCheck struct {
	FacilityId          uint64
	FacilityProfileId   uint64
	FacilityFileId      uint64
	JobId               uint64
	JobApplicationId    uint64
	JobScheduleId       uint64
	DocumentId          uint64
	DocumentFileId      uint64
	FacilityPrimaryUser string // Y N
}

func (con CommonController) CheckCanAccess(nc xapp.NGinCtx, db *gorm.DB, req ReqCommonCheck) bool {
	var facilityProfile model.FacilityProfile
	var facilityId uint64

	if req.JobId > 0 || req.JobApplicationId > 0 || req.DocumentId > 0 || req.JobScheduleId > 0 || req.DocumentFileId > 0 {
		// 只有這5種Id才走以下代碼
		var user xmodel.User
		var err error
		if err = db.Where("id = ?", nc.GetJWTUserId()).First(&user).Error; err != nil {
			return false
		}
		if user.UserType == model.UserUserTypeFacilityUser {
			// 只有機構才需要篩選
			var departments []model.Department
			builder := db.Select("d.*").Table("facility_user_department fud").
				Joins("JOIN department AS d on d.id = fud.department_id").
				Where("fud.user_id = ?", nc.GetJWTUserId())
			if req.FacilityId > 0 {
				builder = builder.Where("d.facility_id = ?", req.FacilityId)
			}
			if err = builder.
				Find(&departments).Error; err != nil {
				return false
			}
			departmentIds := make([]uint64, 0)
			for _, department := range departments {
				if department.AccessAllDepartment == "Y" {
					// 無需限制
					return true
				} else {
					departmentIds = append(departmentIds, department.Id)
				}
			}
			if len(departmentIds) == 0 {
				return false
			}
			// 根據傳輸參數限制
			if req.JobScheduleId > 0 {
				var job model.Job
				if err = db.Where("schedule_template = ?", model.JobScheduleTemplateY).Where("job_schedule_id = ?", req.DocumentId).First(&job).Error; err != nil {
					return false
				}
				req.JobId = job.Id
			}
			if req.DocumentFileId > 0 {
				var documentFileRel model.DocumentFileRelation
				if err = db.Where("document_file_id = ?", req.DocumentFileId).First(&documentFileRel).Error; err != nil {
					return false
				}
				req.DocumentId = documentFileRel.DocumentId
			}
			if req.DocumentId > 0 {
				var document model.Document
				if err = db.Where("id = ?", req.DocumentId).First(&document).Error; err != nil {
					return false
				}
				req.JobApplicationId = document.JobApplicationId
			}
			if req.JobApplicationId > 0 {
				var jobApplication model.JobApplication
				if err = db.Where("id = ?", req.JobApplicationId).First(&jobApplication).Error; err != nil {
					return false
				}
				req.JobId = jobApplication.JobId
			}
			if req.JobId > 0 {
				var relQty int64
				if err = db.Model(&model.JobDepartment{}).Where("department_id IN (?)", departmentIds).Where("job_id = ?", req.JobId).Count(&relQty).Error; err != nil {
					return false
				}
				if relQty == 0 {
					return false
				} else {
					return true
				}
			}
		}
	}

	if req.FacilityProfileId > 0 {
		if err := db.First(&facilityProfile, req.FacilityProfileId).Error; xgorm.IsSqlErr(err) {
			return false
		}
		if facilityProfile.Id == 0 {
			return false
		}
		facilityId = facilityProfile.FacilityId
	}
	if req.FacilityId > 0 {
		if facilityProfile.FacilityId > 0 && facilityProfile.FacilityId != req.FacilityId {
			return false
		}
		facilityId = req.FacilityId
	}
	if req.FacilityFileId > 0 {
		var facilityFile model.FacilityFile
		if err := db.First(&facilityFile, req.FacilityFileId).Error; xgorm.IsSqlErr(err) {
			return false
		}
		if facilityFile.Id == 0 {
			return false
		}
		facilityId = facilityFile.FacilityId
	}
	if facilityId > 0 {
		var facilityUser model.FacilityUser
		builder := db.Where("user_id = ?", nc.GetJWTUserId()).Where("facility_id = ?", facilityId)
		if req.FacilityPrimaryUser != "" {
			builder = builder.Where("primary_user = ?", req.FacilityPrimaryUser)
		}
		if err := builder.First(&facilityUser).Error; xgorm.IsSqlErr(err) {
			return false
		}
		if facilityUser.Id == 0 {
			return false
		}
		return true
	}
	return false
}

const (
	cacheKeyUserFacilityId     = "cache:facility_id:user:%d"
	cacheKeyUserProfessionalId = "cache:professional_id:user:%d"
)

func (con CommonController) GetUserFacilityId(nc xapp.NGinCtx, db *gorm.DB) (uint64, error) {
	cacheKey := fmt.Sprintf(cacheKeyUserFacilityId, nc.GetJWTUserId())
	var facilityId uint64
	exist, err := xredis.GetStruct(nc.C, cacheKey, &facilityId)
	if err != nil {
		return 0, err
	}
	if exist {
		return facilityId, nil
	}
	var facilityUser model.FacilityUser
	if err = db.Where("user_id = ?", nc.GetJWTUserId()).First(&facilityUser).Error; err != nil {
		return 0, err
	}
	if err = xredis.SetStruct(nc.C, cacheKey, facilityUser.FacilityId, time.Minute); err != nil {
		return 0, err
	}
	return facilityUser.FacilityId, nil
}

func (con CommonController) GetUserDraftProfessionalId(nc xapp.NGinCtx, db *gorm.DB) (uint64, error) {
	cacheKey := fmt.Sprintf(cacheKeyUserProfessionalId, nc.GetJWTUserId())
	var professionalId uint64
	exist, err := xredis.GetStruct(nc.C, cacheKey, &professionalId)
	if err != nil {
		return 0, err
	}
	if exist {
		return professionalId, nil
	}
	var professional model.Professional
	if err := db.Where("user_id = ?", nc.GetJWTUserId()).
		Where("data_type = ?", model.ProfessionalDataTypeDraft).
		First(&professional).Error; xgorm.IsSqlErr(err) {
		return 0, err
	}
	if professional.Id == 0 {
		return 0, errors.New("user is not a professional")
	}
	if err := xredis.SetStruct(nc.C, cacheKey, professional.Id, 60*60*24); err != nil {
		return 0, err
	}
	return professional.Id, nil
}

// 檢查單一選項是否存在
func (con CommonController) CheckSelectionExist(checker *xapp.XChecker, db *gorm.DB, selectionType string, selectionValue string) {
	checker.
		Run(func() (bool, i18n.Message, error) {
			if selectionValue == "" {
				return true, i18n.Message{}, nil
			}
			return services.SelectionService.CheckSelectionExist(db, &model.Selection{}, selectionType, selectionValue, model.SelectionStatusEnable)
		})
}

// 檢查多個選項是否存在
func (con CommonController) CheckSelectionsExist(checker *xapp.XChecker, db *gorm.DB, selectionType string, selectionValues string) {
	checker.
		Run(func() (bool, i18n.Message, error) {
			if selectionValues == "" {
				return true, i18n.Message{}, nil
			}
			return services.SelectionService.CheckSelectionsExist(db, selectionType, selectionValues, model.SelectionStatusEnable)
		})
}
