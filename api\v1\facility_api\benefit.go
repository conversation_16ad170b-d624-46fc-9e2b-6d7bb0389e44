package facility_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type BenefitController struct {
	v1.CommonController
}

func NewBenefitController() BenefitController {
	return BenefitController{}
}

// @Tags Benefit
// @Summary 新增機構的福利管理
// @Description
// @Router /v1/facility/benefits/actions/create [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.BenefitCreateReq true "parameter"
// @Success 200 {object} services.BenefitCreateResp "Success"
func (con BenefitController) Create(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.BenefitCreateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		tx := db.Begin()
		resp, err := services.BenefitService.Create(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Benefit
// @Summary 獲取機構的福利管理列表
// @Description
// @Router /v1/facility/benefits [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.BenefitListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Success 200 {object} []services.BenefitListResp "Success"
func (con BenefitController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.BenefitListReq
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.BenefitService.List(db, req, &pageSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Benefit
// @Summary 搜索機構的福利管理
// @Description
// @Router /v1/facility/benefits/actions/search [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.BenefitSearchReq true "parameter"
// @Success 200 {object} []services.BenefitSearchResp "Success"
func (con BenefitController) Search(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.BenefitSearchReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.BenefitService.Search(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Benefit
// @Summary 修改機構的福利管理
// @Description
// @Router /v1/facility/benefits/actions/edit [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.BenefitEditReq true "parameter"
// @Success 200 "Success"
func (con BenefitController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.BenefitEditReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}
		var benefit model.Benefit
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.BenefitService.CheckIdExist(db, &benefit, req.BenefitId, req.FacilityId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.BenefitService.Edit(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Benefit
// @Summary 查询機構的福利管理
// @Description
// @Router /v1/facility/benefits/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.BenefitInquireReq true "parameter"
// @Success 200 {object} services.BenefitInquireResp "Success"
func (con BenefitController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.BenefitInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var benefit model.Benefit
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.BenefitService.CheckIdExist(db, &benefit, req.BenefitId, req.FacilityId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: benefit.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.BenefitService.Inquire(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Benefit
// @Summary 删除機構的福利管理
// @Description
// @Router /v1/facility/benefits/actions/delete [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.BenefitDeleteReq true "parameter"
// @Success 200 "Success"
func (con BenefitController) Delete(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.BenefitDeleteReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var checkMsg []string
		var benefit model.Benefit
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.BenefitService.CheckIdExist(db, &benefit, req.BenefitId, req.FacilityId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.BenefitService.Delete(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}
