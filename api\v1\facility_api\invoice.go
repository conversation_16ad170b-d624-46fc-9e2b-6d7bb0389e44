package facility_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type InvoiceController struct {
	v1.CommonController
}

func NewInvoiceController() InvoiceController {
	return InvoiceController{}
}

// @Tags Invoice
// @Summary 獲取機構發票列表
// @Description
// @Router /v1/facility/invoices [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.FacilityInvoiceListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Param sortingKey query string false "documentDate:文件日期,grandTotal:Amount"
// @Param sortingType query string false "a=順序,d=倒序"
// @Success 200 {object} services.FacilityInvoiceListSummaryResp "Success"
func (con InvoiceController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityInvoiceListReq
	var pageSet xresp.PageSet
	var sortSet xresp.SortingSet
	_ = c.ShouldBindQuery(&pageSet)
	_ = c.ShouldBindQuery(&sortSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		facilityId, err := con.GetUserFacilityId(nc, db)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		req.ReqUserId = nc.GetJWTUserId()
		resp, err := services.InvoiceService.FacilityList(db, req, facilityId, &pageSet, sortSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Invoice
// @Summary 查詢機構發票詳情
// @Description
// @Router /v1/facility/invoices/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.InvoiceInquireReq true "parameter"
// @Success 200 {object} services.InvoiceInquireResp "Success"
func (con InvoiceController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.InvoiceInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{DocumentId: req.DocumentId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var document model.Document
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.DocumentService.CheckIdExistByUserId(db, &document, req.DocumentId, nc.GetJWTUserId(), model.UserUserTypeFacilityUser)
			})
		msg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(msg) > 0 {
			nc.BadRequestResponseWithCheckMsg(msg)
			return
		}

		resp, err := services.InvoiceService.Inquire(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
