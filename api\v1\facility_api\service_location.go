package facility_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type ServiceLocationController struct {
	v1.CommonController
}

func NewServiceLocationController() ServiceLocationController {
	return ServiceLocationController{}
}

// @Tags Service Location
// @Summary 新增機構的服務地點管理
// @Description
// @Router /v1/facility/service-locations/actions/create [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ServiceLocationCreateReq true "parameter"
// @Success 200 {object} services.ServiceLocationCreateResp "Success"
func (con ServiceLocationController) Create(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ServiceLocationCreateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		tx := db.Begin()
		resp, err := services.ServiceLocationService.Create(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Service Location
// @Summary 獲取機構的服務地點管理列表
// @Description
// @Router /v1/facility/service-locations [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.ServiceLocationListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Success 200 {object} []services.ServiceLocationListResp "Success"
func (con ServiceLocationController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ServiceLocationListReq
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.ServiceLocationService.List(db, req, &pageSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Service Location
// @Summary 搜索機構的服務地點管理
// @Description
// @Router /v1/facility/service-locations/actions/search [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.ServiceLocationSearchReq true "parameter"
// @Success 200 {object} []services.ServiceLocationSearchResp "Success"
func (con ServiceLocationController) Search(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ServiceLocationSearchReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		resp, err := services.ServiceLocationService.Search(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Service Location
// @Summary 修改機構的服務地點管理
// @Description
// @Router /v1/facility/service-locations/actions/edit [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ServiceLocationEditReq true "parameter"
// @Success 200 "Success"
func (con ServiceLocationController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ServiceLocationEditReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}
		var serviceLocation model.ServiceLocation
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.ServiceLocationService.CheckIdExist(db, &serviceLocation, req.ServiceLocationId, req.FacilityId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.ServiceLocationService.Edit(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Service Location
// @Summary 查询機構的服務地點管理
// @Description
// @Router /v1/facility/service-locations/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.ServiceLocationInquireReq true "parameter"
// @Success 200 {object} services.ServiceLocationInquireResp "Success"
func (con ServiceLocationController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ServiceLocationInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var serviceLocation model.ServiceLocation
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.ServiceLocationService.CheckIdExist(db, &serviceLocation, req.ServiceLocationId, req.FacilityId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		resp, err := services.ServiceLocationService.Inquire(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Service Location
// @Summary 删除機構的服務地點管理
// @Description
// @Router /v1/facility/service-locations/actions/delete [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ServiceLocationDeleteReq true "parameter"
// @Success 200 "Success"
func (con ServiceLocationController) Delete(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ServiceLocationDeleteReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var checkMsg []string
		var serviceLocation model.ServiceLocation
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.ServiceLocationService.CheckIdExist(db, &serviceLocation, req.ServiceLocationId, req.FacilityId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.ServiceLocationService.Delete(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}
