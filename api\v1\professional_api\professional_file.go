package professional_api

import (
	"fmt"
	"net/http"
	"path"

	"github.com/nicksnyder/go-i18n/v2/i18n"

	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	"github.com/gin-gonic/gin"
)

type ProfessionalFileController struct {
	v1.CommonController
}

func NewProfessionalFileController() ProfessionalFileController {
	return ProfessionalFileController{}
}

// @Tags Professional File
// @Summary 獲取專業人士文件圖片
// @Description 直接獲取專業人士文件的圖片內容，可以在瀏覽器中直接顯示
// @Router /v1/professional/professional-files/actions/preview [GET]
// @Security ApiKeyAuth
// @Param professionalFileId query uint64 true "專業人士文件Id"
// @Param thumb query string true "是否獲取縮略圖 Y=縮略圖 N=原圖"
// @Success 200 "Success"
func (con ProfessionalFileController) Preview(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalFileGetPreviewReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalFileService.CheckFileExist(db, nc.GetJWTUserId(), []uint64{req.ProfessionalFileId})
			})
		if nc.BadRequestResponseIfCheckerFailed(checker, req) {
			return
		}
		// 獲取圖片數據
		resp, err := services.ProfessionalFileService.Preview(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		c.Writer.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename*=utf-8''%s", xtool.ReplacePlus(resp.Filename)))
		c.Data(http.StatusOK, services.FacilityFileService.GetFileMimeType(resp.Filename), resp.FileBytes)

	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional File
// @Summary 專業人士文件上傳文件
// @Description
// @Router /v1/professional/professional-files/actions/upload-file [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalFileUploadFileReq true "parameter"
// @Param file formData file true "file"
// @Success 200 {object} services.ProfessionalFileUploadFileResp "Success"
func (con ProfessionalFileController) UploadFile(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var err error
	var req services.ProfessionalFileUploadFileReq
	if err = c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		if req.ProfessionalId, err = con.GetUserDraftProfessionalId(nc, db); err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		file, err := c.FormFile("file")
		if err != nil {
			nc.BadRequestResponse(err)
			return
		}
		checker := xapp.NewCK(c, true)
		checker.
			RunWithTemplateData(func() (bool, i18n.Message, map[string]string, error) {
				return services.ProfessionalFileService.CheckFileSize(req.FileCode, file.Size)
			}).
			RunWithTemplateData(func() (bool, i18n.Message, map[string]string, error) {
				return services.ProfessionalFileService.CheckFileType(req.FileCode, path.Ext(file.Filename))
			})
		checkMsg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.AlertResponse(checkMsg)
			return
		}

		req.File = file
		if services.ProfessionalAiFileMap[req.FileCode] {
			aiResp, err := services.AiService.AiDateAndNumber(db, file)
			if err != nil {
				nc.ErrorResponse(req, err)
				return
			}
			req.AiDateAndNumber = aiResp
		}

		tx := db.Begin()
		resp, err := services.ProfessionalFileService.UploadFile(tx, req, nc.GetJWTUserId())
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
