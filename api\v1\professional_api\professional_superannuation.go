package professional_api

import (
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type ProfessionalSuperannuationController struct{}

func NewProfessionalSuperannuationController() ProfessionalSuperannuationController {
	return ProfessionalSuperannuationController{}
}

// @Tags Superannuation
// @Summary 编辑退休金信息
// @Description
// @Router /v1/professional/superannuation/actions/edit [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalSuperannuationEditReq true "parameter"
// @Success 200 "Success"
func (con ProfessionalSuperannuationController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var err error
	var req services.ProfessionalSuperannuationEditReq
	var draftReq services.ProfessionalSuperannuationEditDraftReq

	// 先嘗試以草稿模式綁定，檢查是否為草稿模式
	if err = c.ShouldBindBodyWith(&draftReq, binding.JSON); err != nil {
		nc.BadRequestResponse(err)
		return
	}
	if draftReq.DataType == model.ProfessionalSuperannuationDataTypeDraft {
		_ = copier.Copy(&req, draftReq)
	} else if err = c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		nc.BadRequestResponse(err)
		return
	}
	db := xgorm.DB.WithContext(c)

	userId := nc.GetJWTUserId()
	checker := xapp.NewCK(c)
	checker.
		Run(func() (bool, i18n.Message, error) {
			return services.ProfessionalSuperannuationService.CheckIdExist(db, &model.ProfessionalSuperannuation{}, req.ProfessionalSuperannuationId, userId)
		}).
		Run(func() (bool, i18n.Message, error) {
			return services.ProfessionalSuperannuationService.CheckDeclarationConfirmed(req)
		})
	if req.ProfessionalFileId > 0 {
		checker.Run(func() (bool, i18n.Message, error) {
			return services.ProfessionalFileService.CheckFileExist(db, userId, []uint64{req.ProfessionalFileId}, []string{model.ProfessionalFileCodeSuperannuation})
		})
	}

	if nc.BadRequestResponseIfCheckerFailed(checker, req) {
		return
	}

	err = services.ProfessionalSuperannuationService.Edit(db, req, userId)
	if err != nil {
		if xgorm.IsNotFoundErr(err) {
			nc.BadRequestResponse(err)
			return
		}
		nc.ErrorResponse(req, err)
		return
	}
	nc.OKResponse(nil)
}

// @Tags Superannuation
// @Summary 查询退休金信息
// @Description
// @Router /v1/professional/superannuation/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.ProfessionalSuperannuationInquireReq true "parameter"
// @Success 200 {object} services.ProfessionalSuperannuationInquireResp "Success"
func (con ProfessionalSuperannuationController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	userId := nc.GetJWTUserId()
	var req services.ProfessionalSuperannuationInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.UserId = userId
		req.DataType = model.ProfessionalSuperannuationDataTypeDraft
		resp, err := services.ProfessionalSuperannuationService.Inquire(db, req)
		if xgorm.IsNotFoundErr(err) {
			nc.BadRequestResponse(err)
			return
		}
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
