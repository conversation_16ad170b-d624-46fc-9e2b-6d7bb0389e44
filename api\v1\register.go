package v1

import (
	"errors"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xi18n"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	log "github.com/sirupsen/logrus"
)

type RegisterController struct {
	CommonController
}

func NewRegisterController() RegisterController {
	return RegisterController{}
}

// @Tags Register
// @Summary 機構註冊
// @Description 註冊新的機構用戶
// @Router /v1/register/facility [POST]
// @Produce json
// @Param json body services.RegisterFacilityReq true "parameter"
// @Success 200 {object} services.RegisterFacilityResp "Success"
func (c RegisterController) RegisterFacility(ctx *gin.Context) {
	nc := xapp.NGinCtx{C: ctx}
	var req services.RegisterFacilityReq
	if e := ctx.ShouldBindJSON(&req); e == nil {
		db := xgorm.DB.WithContext(ctx)

		checker := xapp.NewCK(ctx, true)
		checker.Run(func() (bool, i18n.Message, error) {
			return services.ReCaptchaService.CheckReCaptcha(db, req.RecaptchaToken, ctx.ClientIP())
		}).Run(func() (bool, i18n.Message, error) {
			return services.RegisterService.CheckUserEmailUnique(db, req.Email)
		})

		if nc.BadRequestResponseIfCheckerFailed(checker, req) {
			return
		}
		result, err := services.RegisterService.RegisterFacility(ctx, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(result)
	} else {
		nc.BadRequestResponse(e)
	}
}

// @Tags Register
// @Summary 機構驗證電郵確認註冊
// @Description 驗證機構電郵確認註冊
// @Router /v1/register/facility-verify-email [POST]
// @Produce json
// @Param json body services.RegisterVerifyFacilityEmailReq true "parameter"
// @Success 200 "Success"
// @Success 20003 "Register record expired"
// @Success 20004 "Register verify code expired"
func (c RegisterController) RegisterFacilityVerifyEmail(ctx *gin.Context) {
	nc := xapp.NGinCtx{C: ctx}
	var req services.RegisterVerifyFacilityEmailReq
	if err := ctx.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(ctx)

		var registerInfo services.RegisterFacilityInfo
		var msg i18n.Message
		var exist bool
		exist, msg, err = services.RegisterService.CheckerRegisterInfoExists(ctx, &registerInfo, services.RegisterUserTypeFacility, req.EmailVerifyUuid)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if !exist {
			// 20003 註冊記錄已過期
			nc.Response(AuthenticationRegisterRecordExpired, []string{xi18n.Localize(ctx.Request, &msg)}, nil)
			return
		}
		var valid bool
		valid, msg, err = services.RegisterService.CheckerRegisterCodeExpired(services.RegisterUserTypeFacility, registerInfo)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if !valid {
			// 20004 註冊驗證碼已過期
			nc.Response(AuthenticationRegisterVerifyCodeExpired, []string{xi18n.Localize(ctx.Request, &msg)}, nil)
			return
		}
		valid, msg, err = services.RegisterService.CheckerRegisterVerificationCode(services.RegisterUserTypeFacility, registerInfo, req.EmailVerifyCode)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if !valid {
			// 20004 註冊驗證碼無效
			nc.Response(AuthenticationRegisterVerifyCodeExpired, []string{xi18n.Localize(ctx.Request, &msg)}, nil)
			return
		}
		// 再次檢查Email是否已經註冊
		valid, msg, err = services.RegisterService.CheckUserEmailUnique(db, registerInfo.Email)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if !valid {
			// 20006 電郵已註冊
			_ = services.RegisterService.DeleteRegisterInfo(ctx, services.RegisterUserTypeFacility, req.EmailVerifyUuid)
			nc.Response(AuthenticationEmailAlreadyRegistered, []string{xi18n.Localize(ctx.Request, &msg)}, nil)
			return
		}
		tx := db.Begin()
		err = services.RegisterService.RegisterVerifyFacilityEmail(tx, registerInfo)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		// 刪除註冊記錄緩存
		if err = services.RegisterService.DeleteRegisterInfo(ctx, services.RegisterUserTypeFacility, req.EmailVerifyUuid); err != nil {
			log.Warnf("Delete register info failed: userType=%s, emailVerifyUuid=%s, err=%v", services.RegisterUserTypeFacility, req.EmailVerifyUuid, err)
		}
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Register
// @Summary 專業人士註冊
// @Description 註冊新的專業人士用戶
// @Router /v1/register/professional [POST]
// @Produce json
// @Param json body services.RegisterProfessionalReq true "parameter"
// @Success 200 {object} services.RegisterProfessionalResp "Success"
func (c RegisterController) RegisterProfessional(ctx *gin.Context) {
	nc := xapp.NGinCtx{C: ctx}
	var req services.RegisterProfessionalReq
	if err := ctx.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(ctx)

		checker := xapp.NewCK(ctx, true)
		checker.Run(func() (bool, i18n.Message, error) {
			return services.ReCaptchaService.CheckReCaptcha(db, req.RecaptchaToken, ctx.ClientIP())
		}).Run(func() (bool, i18n.Message, error) {
			return services.RegisterService.CheckUserEmailUnique(db, req.Email)
		})

		if nc.BadRequestResponseIfCheckerFailed(checker, req) {
			return
		}
		resp, err := services.RegisterService.RegisterProfessional(ctx, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Register
// @Summary 專業人士驗證電郵確認註冊
// @Description 驗證專業人士電郵確認註冊
// @Router /v1/register/professional-verify-email [POST]
// @Produce json
// @Param json body services.RegisterVerifyProfessionalEmailReq true "parameter"
// @Success 200 "Success"
// @Success 20003 "Register record expired"
// @Success 20004 "Register verify code expired"
func (c RegisterController) RegisterProfessionalVerifyEmail(ctx *gin.Context) {
	nc := xapp.NGinCtx{C: ctx}
	var req services.RegisterVerifyProfessionalEmailReq
	if err := ctx.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(ctx)

		var registerInfo services.RegisterProfessionalInfo
		var msg i18n.Message
		var exist bool
		exist, msg, err = services.RegisterService.CheckerRegisterInfoExists(ctx, &registerInfo, services.RegisterUserTypeProfessional, req.EmailVerifyUuid)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if !exist {
			// 20003 註冊記錄已過期
			nc.Response(AuthenticationRegisterRecordExpired, []string{xi18n.Localize(ctx.Request, &msg)}, nil)
			return
		}
		var valid bool
		valid, msg, err = services.RegisterService.CheckerRegisterCodeExpired(services.RegisterUserTypeProfessional, registerInfo)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if !valid {
			// 20004 註冊驗證碼已過期
			nc.Response(AuthenticationRegisterVerifyCodeExpired, []string{xi18n.Localize(ctx.Request, &msg)}, nil)
			return
		}
		valid, msg, err = services.RegisterService.CheckerRegisterVerificationCode(services.RegisterUserTypeProfessional, registerInfo, req.EmailVerifyCode)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if !valid {
			// 20004 註冊驗證碼無效
			nc.Response(AuthenticationRegisterVerifyCodeExpired, []string{xi18n.Localize(ctx.Request, &msg)}, nil)
			return
		}
		// 再次檢查Email是否已經註冊
		valid, msg, err = services.RegisterService.CheckUserEmailUnique(db, registerInfo.Email)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if !valid {
			// 20006 電郵已註冊
			_ = services.RegisterService.DeleteRegisterInfo(ctx, services.RegisterUserTypeProfessional, req.EmailVerifyUuid)
			nc.Response(AuthenticationEmailAlreadyRegistered, []string{xi18n.Localize(ctx.Request, &msg)}, nil)
			return
		}

		tx := db.Begin()
		_, err = services.RegisterService.RegisterVerifyProfessionalEmail(tx, registerInfo)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		// 刪除註冊記錄緩存
		if err = services.RegisterService.DeleteRegisterInfo(ctx, services.RegisterUserTypeProfessional, req.EmailVerifyUuid); err != nil {
			log.Warnf("Delete register info failed: userType=%s, emailVerifyUuid=%s, err=%v", services.RegisterUserTypeProfessional, req.EmailVerifyUuid, err)
		}
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Register
// @Summary 重發驗證電郵
// @Description 重發驗證電郵
// @Router /v1/register/resend-verification-email [POST]
// @Produce json
// @Param json body services.ResendVerificationEmailReq true "parameter"
// @Success 200 "Success"
// @Success 20003 "Register record expired"
// @Success 20004 "Register verify code expired"
func (c RegisterController) ResendVerificationEmail(ctx *gin.Context) {
	nc := xapp.NGinCtx{C: ctx}
	var req services.ResendVerificationEmailReq
	if err := ctx.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(ctx)

		var professionalInfo services.RegisterProfessionalInfo
		var facilityInfo services.RegisterFacilityInfo
		var msg i18n.Message
		var exist bool
		switch req.UserType {
		case services.RegisterUserTypeProfessional:
			exist, msg, err = services.RegisterService.CheckerRegisterInfoExists(ctx, &professionalInfo, req.UserType, req.EmailVerifyUuid)
		case services.RegisterUserTypeFacility:
			exist, msg, err = services.RegisterService.CheckerRegisterInfoExists(ctx, &facilityInfo, req.UserType, req.EmailVerifyUuid)
		default:
			nc.ErrorResponse(req, errors.New("invalid user type"))
			return
		}
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if !exist {
			// 20003 註冊記錄已過期
			nc.Response(AuthenticationRegisterRecordExpired, []string{xi18n.Localize(ctx.Request, &msg)}, nil)
			return
		}
		var valid bool
		if req.UserType == services.RegisterUserTypeProfessional {
			valid, msg, err = services.RegisterService.CheckerRegisterCodeSendCount(req.UserType, professionalInfo)
		} else {
			valid, msg, err = services.RegisterService.CheckerRegisterCodeSendCount(req.UserType, facilityInfo)
		}
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if !valid {
			// 20005 註冊驗證碼發送次數限制
			_ = services.RegisterService.DeleteRegisterInfo(ctx, req.UserType, req.EmailVerifyUuid)
			nc.Response(AuthenticationRegisterCodeSendCountLimit, []string{xi18n.Localize(ctx.Request, &msg)}, nil)
			return
		}
		// 再次檢查Email是否已經註冊
		var email string
		switch req.UserType {
		case services.RegisterUserTypeFacility:
			email = facilityInfo.Email
		case services.RegisterUserTypeProfessional:
			email = professionalInfo.Email
		}
		valid, msg, err = services.RegisterService.CheckUserEmailUnique(db, email)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if !valid {
			// 20006 電郵已註冊
			_ = services.RegisterService.DeleteRegisterInfo(ctx, req.UserType, req.EmailVerifyUuid)
			nc.Response(AuthenticationEmailAlreadyRegistered, []string{xi18n.Localize(ctx.Request, &msg)}, nil)
			return
		}

		resp, err := services.RegisterService.ResendVerificationEmail(ctx, req.UserType, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
