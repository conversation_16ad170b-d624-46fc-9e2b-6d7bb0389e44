package system_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/shopspring/decimal"
)

type InvoiceController struct {
	v1.CommonController
}

func NewInvoiceController() InvoiceController {
	return InvoiceController{}
}

// @Tags Invoice
// @Summary 獲取發票列表
// @Description
// @Router /v1/system/invoices [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.SystemInvoiceListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Param sortingKey query string false "documentDate:文件日期,grandTotal:Amount"
// @Param sortingType query string false "a=順序,d=倒序"
// @Success 200 {object} services.FacilityInvoiceListSummaryResp "Success"
func (con InvoiceController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.SystemInvoiceListReq
	var pageSet xresp.PageSet
	var sortSet xresp.SortingSet
	_ = c.ShouldBindQuery(&pageSet)
	_ = c.ShouldBindQuery(&sortSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.InvoiceService.SystemList(db, req, &pageSet, sortSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Invoice
// @Summary 查詢機構發票詳情
// @Description
// @Router /v1/system/invoices/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.InvoiceInquireReq true "parameter"
// @Success 200 {object} services.InvoiceInquireResp "Success"
func (con InvoiceController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.InvoiceInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var document model.Document
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.DocumentService.CheckIdExist(db, &document, req.DocumentId)
			})
		msg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(msg) > 0 {
			nc.BadRequestResponseWithCheckMsg(msg)
			return
		}

		resp, err := services.InvoiceService.Inquire(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Invoice
// @Summary 創建發票
// @Description
// @Router /v1/system/invoices/actions/create [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.InvoiceCreateReq true "parameter"
// @Success 200 {object} services.InvoiceCreateResp "Success"
func (con InvoiceController) Create(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.InvoiceCreateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		taxRate := decimal.NewFromInt(10).Div(decimal.NewFromInt(100))

		var checkMsg []string
		var facility model.Facility
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityService.CheckIdExist(db, &facility, req.FacilityId)
			}).
			Run(func() (bool, i18n.Message, error) {
				checkItemValidReq := services.CheckInvoiceItemValidReq{
					Item:        req.Item,
					TotalAmount: req.TotalAmount,
					TaxAmount:   req.TaxAmount,
					GrandTotal:  req.GrandTotal,
				}
				return services.InvoiceService.CheckInvoiceItemValid(checkItemValidReq, taxRate)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		resp, err := services.InvoiceService.Create(tx, req, taxRate)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Invoice
// @Summary 更新發票
// @Description
// @Router /v1/system/invoices/actions/edit [POST]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.InvoiceEditReq true "parameter"
// @Success 200 "Success"
func (con InvoiceController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.InvoiceEditReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var document model.Document

		// 檢查申請是否可以取消
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.ConfirmationNoteService.CheckIdExist(db, &document, model.DocumentCategoryInvoice, req.DocumentId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.InvoiceService.CheckCanEdit(&document)
			}).
			Run(func() (bool, i18n.Message, error) {
				checkItemValidReq := services.CheckInvoiceItemValidReq{
					Item:        req.Item,
					TotalAmount: req.TotalAmount,
					TaxAmount:   req.TaxAmount,
					GrandTotal:  req.GrandTotal,
				}
				return services.InvoiceService.CheckInvoiceItemValid(checkItemValidReq, document.TaxRate)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()

		err = services.InvoiceService.Edit(tx, document, req, document.TaxRate)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Invoice
// @Summary 確認付款
// @Description
// @Router /v1/system/invoices/actions/confirm-payment [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.InvoiceConfirmPaymentReq true "parameter"
// @Success 200 "Success"
func (con InvoiceController) ConfirmPayment(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.InvoiceConfirmPaymentReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.InvoiceService.CheckCanConfirmPayment(db, req.DocumentIds, req.Action)
			})
		msg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(msg) > 0 {
			nc.BadRequestResponseWithCheckMsg(msg)
			return
		}

		tx := db.Begin()
		err = services.InvoiceService.ConfirmPayment(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}
