package system_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type JobApplicationController struct {
	v1.CommonController
}

func NewJobApplicationController() JobApplicationController {
	return JobApplicationController{}
}

// @Tags JobApplication
// @Summary 查詢工作職位申請列表
// @Description 查詢特定工作職位的申請者列表，支持分頁和條件過濾
// @Router /v1/system/job-applications [GET]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.JobApplicationListForSystemReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Param sortingKey query string false "score 匹配分數"
// @Param sortingType query string false "a=順序,d=倒序"
// @Success 200 {object} []services.JobApplicationListResp "Success"
func (con JobApplicationController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobApplicationListForSystemReq
	var pageSet xresp.PageSet
	var sortSet xresp.SortingSet
	_ = c.ShouldBindQuery(&pageSet)
	_ = c.ShouldBindQuery(&sortSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		req.ReqUserId = nc.GetJWTUserId()
		resp, err := services.JobApplicationService.List(db, req.GetBaseReq(), &pageSet, sortSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags JobApplication
// @Summary 查詢工作職位申請詳情
// @Description 查詢特定工作職位申請的詳細信息
// @Router /v1/system/job-applications/actions/inquire [GET]
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.JobApplicationInquireForSystemReq true "parameter"
// @Success 200 {object} services.JobApplicationInquireResp "Success"
func (con JobApplicationController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.JobApplicationInquireForSystemReq

	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		var application model.JobApplication
		checker := xapp.NewCK(c)
		checker.Run(func() (bool, i18n.Message, error) {
			return services.JobApplicationService.CheckIdExist(db, &application, req.JobApplicationId)
		})

		checkMsg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		resp, err := services.JobApplicationService.Inquire(db, req.GetBaseReq())
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags JobApplication
// @Summary 獲取我的工作職位申請列表
// @Description
// @Router /v1/system/job-applications/actions/my-jobs [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.MyJobListReqBySystem true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Param sortingKey query string false "beginTime 開始时间, applyTime 申請时间, hourlyRate 時薪"
// @Param sortingType query string false "a=順序,d=倒序"
// @Success 200 {object} []services.MyJobListResp "Success"
func (con JobApplicationController) MyJob(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.MyJobListReqBySystem
	var pageSet xresp.PageSet
	var sortSet xresp.SortingSet
	_ = c.ShouldBindQuery(&pageSet)
	_ = c.ShouldBindQuery(&sortSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.JobApplicationService.MyJobList(db, req.GetBaseReq(), &pageSet, sortSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}
