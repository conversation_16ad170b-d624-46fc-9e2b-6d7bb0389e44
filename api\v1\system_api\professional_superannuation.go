package system_api

import (
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

type SuperannuationController struct{}

func NewSuperannuationController() SuperannuationController {
	return SuperannuationController{}
}

// @Tags Superannuation
// @Summary 查询养老金信息
// @Description
// @Router /v1/system/superannuation/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.SuperannuationInquireReq true "parameter"
// @Success 200 {object} services.ProfessionalSuperannuationInquireResp "Success"
func (con SuperannuationController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.SuperannuationInquireReq
	var xreq services.ProfessionalSuperannuationInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.DataType = model.ProfessionalSuperannuationDataTypeSubmitted
		_ = copier.Copy(&xreq, req)
		resp, err := services.ProfessionalSuperannuationService.Inquire(db, xreq)
		if xgorm.IsNotFoundErr(err) {
			nc.BadRequestResponse(err)
			return
		}
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
