[app]
JwtSecret = RxYAuIDEfMq3vy29JiwlnmXPVdo1FzGCKej0BS76rgQbkshH4L8U5aZWNtOcTpLPTgMhPMqttEu6UOYNyZMDm0V55cwhX07KGDX2TFL47bHmTSVgA24gIjUadEkuVqb5
PasswordSalt = medic-crew-2025
Aes256Key = KhdmOcyPjxYI1lT9CW6zkLEw4s0UZ3aqQ5MSXp2bvrBAD7GJVngioR8NHeuFft6tmToHBOMQcfcn4FVEs0QjUszy0mrAbUvxyJD7LQY5hToAB3sOcdh39arTATb4SqY4
AppName = medic-crew
AlertAccessToken =

[swagger]
SwaggerHost = 127.0.0.1
SwaggerHostPort = 80

[server]
RunMode = release
DisableLogColor = true
HttpPort = 80
ReadTimeout = 60
WriteTimeout = 60
AllowOrigins =
HttpProxy =
HttpsProxy =

[redis]
Host = *************:6379
Password = root
DB = 6

[rabbitmq]
Host = *************:5672
User = root
Password = root

[database]
User = medic-crew
Password = qL@z9fYa&g4F
Host = *************:3306
DB = medic_crew_prod
DisableLog = false
ParseTime = true

[postgresql_database]
User =
Password =
Host =
Port =
DB =
Schema =
SslMode =

[mail]
Host = smtpdm.aliyun.com
Port = 465
Username = <EMAIL>
Password = M7hpK8bJrkz5
Name = Medic Crew
InsecureSkipVerify = true

[oss]
Endpoint = ap-south-1.linodeobjects.com
AccessKey = OF1RLRYDUH4BOIEQM03E
SecretKey = XXXXXXXXXXXXXXXXXXXXXXXXXX
Region = ap-south-1
Bucket = medic-crew-dev

[onedrive]
ClientId =
ClientSecret =
DriveId =
RootFolder =

[outlook_mail_oauth]
ClientId =
ClientSecret =
TenantId =

[ai_platform]
GeminiKey = AIzaSyADUeT20drQ-16mo6HrelVOXFGFvFvuUSU
OpenAiKey =
DeepseekKey =

[google_oauth]
ClientId = 535960906552-4es66f42fe7tnf2q4l0hjas54qb74uev.apps.googleusercontent.com
ProjectId =
ClientSecret = 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
RedirectUrl = https://user.mediccrew.com.au/user/user/google-callback
Scopes = profile,email

[google_service]
ApiKey = AIzaSyDN2HBrzpPLtz4kyicqHwAfSYYh3lsYSXU

[ad_oauth]
ClientId =
ProjectId =
ClientSecret =
RedirectUrl =

[recaptcha]
SecretKey = 6LeyGdIrAAAAAOM-3YnR3xuB8bYbsl_mjWkoPj6p
ScoreThreshold = 0.5

