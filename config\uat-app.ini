[app]
JwtSecret = ICyLffaCHXS0kp4QJhiVodZ2EtOTR0ef80C3RVfN8G7Q3ak2HZPZBKspcMFOOmSp
PasswordSalt = medic-crew
Aes256Key = gHVIZg3ZzumB9rydJaPEqDyDbItooUPX5tBSahrx33qOXCiyyOzjZl6cCLQKIoBG
AppName = medic-crew
AlertAccessToken = xxx

[swagger]
SwaggerHost = *************
SwaggerHostPort = 8310

[server]
RunMode = debug
DisableLogColor = true
HttpPort = 8310
ReadTimeout = 60
WriteTimeout = 60
AllowOrigins =
HttpProxy =
HttpsProxy =

[redis]
Host = *************:6379
Password = root
DB = 6

[rabbitmq]
Host = *************:5672
User = root
Password = root

[database]
User = admin
Password = X&g8He!sEWy9
Host = **************:3307
DB = medic_crew_uat
DisableLog = false
ParseTime = true

[postgresql_database]
User =
Password =
Host =
Port =
DB =
Schema =
SslMode =

[mail]
Host = smtpdm.aliyun.com
Port = 465
Username = <EMAIL>
Password = M7hpK8bJrkz5
Name = Medic Crew
InsecureSkipVerify = true

[oss]
Endpoint = ap-south-1.linodeobjects.com
AccessKey = OF1RLRYDUH4BOIEQM03E
SecretKey = 2GwNx3MQzSBzBUP8zq3PMeUxbfohhUEo00xCqKdv
Region = ap-south-1
Bucket = medic-crew-dev

[onedrive]
ClientId =
ClientSecret =
DriveId =
RootFolder =

[outlook_mail_oauth]
ClientId =
ClientSecret =
TenantId =

[ai_platform]
GeminiKey = AIzaSyA9Eua6n_HFto75K3OSgLPsXQMYqDp-Ddw
OpenAiKey =
DeepseekKey =

[google_oauth]
ClientId =
ProjectId =
ClientSecret =
RedirectUrl =
Scopes =

[google_service]
ApiKey = AIzaSyCHxj8pClZk4-bzRQlkTsJH0gPeNhIiqU4

[ad_oauth]
ClientId =
ProjectId =
ClientSecret =
RedirectUrl =

[recaptcha]
SecretKey = 6Le8UgArAAAAAM4FSbOp8lXtiqM3Twwy6Cm8aGJY
ScoreThreshold = 0.5

[sentry]
;Dsn = http://43e5fdc139477f6b6e87e88ce92373f9@*************:9000/3
Environment = backend
Release =
TracesSampleRate = 1.0
ProfilesSampleRate = 1.0
EnableTracing = true