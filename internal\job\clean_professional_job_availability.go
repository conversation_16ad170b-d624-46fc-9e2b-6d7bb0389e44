package job

import (
	"context"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

const CronCleanProfessionalJobAvailability = "cron_clean_professional_job_availability"

// 清理專業職務可提供時間
func cleanProfessionalJobAvailability() {
	traceId := uuid.NewV4().String()
	ctx := context.Background()
	ctx = context.WithValue(ctx, "traceId", traceId)

	logger := log.WithField("traceId", traceId).WithField("task", CronCleanProfessionalJobAvailability)

	db := xgorm.DB.WithContext(ctx)
	run, _, err := services.CronSettingService.CheckCronRunning(db, CronCleanProfessionalJobAvailability)
	if err != nil {
		logger.Errorf("[CRON] fail to clean professional job availability: %v", err)
		return
	}
	if !run {
		logger.Warnf("[CRON] <%s> cron job not run ", CronCleanProfessionalJobAvailability)
		return
	}

	currentDate := xtool.NowFormat(xtool.DateDayA)

	// 在事務中執行清理操作
	err = db.Transaction(func(tx *gorm.DB) error {
		return tx.Where("filter_type = ? OR filter_type = ?", model.ProfessionalJobAvailabilityFilterTypeAvailableDate, model.ProfessionalJobAvailabilityFilterTypeUnavailableDate).
			Where("end_date < ?", currentDate).Delete(&model.ProfessionalJobAvailability{}).Error
	})

	if err != nil {
		logger.Errorf("[CRON] fail to clean professional job availability: %v", err)
		return
	}

	logger.Info("professional job availability cleanup completed")
}
