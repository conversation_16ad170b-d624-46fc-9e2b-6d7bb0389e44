package job

import (
	"context"
	"encoding/json"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xi18n"
	"github.com/Norray/xrocket/xmodel/xtype"
	"github.com/Norray/xrocket/xtool"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

const (
	CronCheckProfessionalDocumentExpiry                    = "cron_check_professional_document_expiry" // 檢查專業人員文件到期
	CheckProfessionalDocumentExpiryMaxProcessRecordsPerRun = 100                                       // 每次處理的最大記錄數
	CheckProfessionalDocumentExpiryLockTimeoutSeconds      = 50                                        // 鎖定超時時間（秒）
)

// 檢查專業人員文件到期定時任務 - 每天0點執行
func jobCheckProfessionalDocumentExpiry() {
	traceId := uuid.NewV4().String()
	ctx := context.Background()
	ctx = context.WithValue(ctx, "traceId", traceId)

	logger := log.WithField("traceId", traceId).WithField("task", CronCheckProfessionalDocumentExpiry)

	db := xgorm.DB.WithContext(ctx)
	run, _, err := services.CronSettingService.CheckCronRunning(db, CronCheckProfessionalDocumentExpiry)
	if err != nil {
		logger.Errorf("[CRON] fail to check document expiry task: %v", err)
		return
	}
	if !run {
		logger.Warnf("[CRON] <%s> cron job not run ", CronCheckProfessionalDocumentExpiry)
		return
	}

	nowTime := time.Now() // 這裡以服務器時間作為日期
	thirtyDaysLater := nowTime.AddDate(0, 0, 30)

	// 檢查即將到期的文件（30天內到期）
	checkExpiringDocuments(db, nowTime, thirtyDaysLater, logger)

	logger.Info("document expiry check completed")
}

// 檢查即將到期的文件
func checkExpiringDocuments(db *gorm.DB, nowTime, thirtyDaysLater time.Time, logger *log.Entry) {
	// 開啟事務
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logger.Errorf("[CRON] transaction rollback due to panic: %v", r)
		}
	}()

	// 收集所有需要發送通知的請求
	var notificationReqs []services.CreateProfessionalProfileDocumentExpireReq

	// 檢查 AHPRA 證書到期
	ahpraReqs := getAhpraExpiryNotifications(tx, nowTime, thirtyDaysLater, logger)
	notificationReqs = append(notificationReqs, ahpraReqs...)

	// 檢查身份檢查文件到期
	idCheckReqs := getIdCheckExpiryNotifications(tx, nowTime, thirtyDaysLater, logger)
	notificationReqs = append(notificationReqs, idCheckReqs...)

	// 過濾重複的 ProfessionalId，避免同一專業人員收到多個通知
	uniqueReqs := filterDuplicateProfessionalIds(notificationReqs)

	// 批量發送通知
	if len(uniqueReqs) > 0 {
		for _, req := range uniqueReqs {
			if err := services.SystemNotificationService.CreateProfessionalProfileDocumentExpire(tx, req); err != nil {
				tx.Rollback()
				logger.Errorf("[CRON] fail to send document expiry notification to user %d: %v", req.ProfessionalUserId, err)
				return
			}
		}
		logger.Infof("Successfully sent %d document expiry notifications (filtered from %d)", len(uniqueReqs), len(notificationReqs))
	}

	// 提交事務
	if err := tx.Commit().Error; err != nil {
		logger.Errorf("[CRON] fail to commit transaction: %v", err)
		return
	}
}

// 過濾重複的 ProfessionalId，保留第一個請求
func filterDuplicateProfessionalIds(reqs []services.CreateProfessionalProfileDocumentExpireReq) []services.CreateProfessionalProfileDocumentExpireReq {
	seen := make(map[uint64]bool)
	var uniqueReqs []services.CreateProfessionalProfileDocumentExpireReq

	for _, req := range reqs {
		if !seen[req.ProfessionalId] {
			seen[req.ProfessionalId] = true
			uniqueReqs = append(uniqueReqs, req)
		}
	}

	return uniqueReqs
}

// 獲取 AHPRA 證書到期通知請求
func getAhpraExpiryNotifications(db *gorm.DB, nowTime, thirtyDaysLater time.Time, logger *log.Entry) []services.CreateProfessionalProfileDocumentExpireReq {
	var professionals []struct {
		ProfessionalId  uint64         `json:"professionalId"`
		UserId          uint64         `json:"userId"`
		AhpraExpiryDate xtype.NullDate `json:"ahpraExpiryDate"`
	}

	// 查詢 AHPRA 證書即將到期的專業人士（排除已發送通知的）
	// professional_id + SystemNotificationRelatedTypeProfessionalFile 作為唯一key，避免重複發送通知
	builder := db.Table("professional AS p").
		Select([]string{
			"p.id AS professional_id",
			"p.user_id",
			"p.ahpra_expiry_date",
		}).
		Joins("LEFT JOIN system_notification sn ON sn.related_id = p.id AND sn.notification_type = ? AND sn.related_type = ?",
			model.SystemNotificationTypeProfessionalProfileDocumentExpire, model.SystemNotificationRelatedTypeProfessionalFile).
		Joins("LEFT JOIN system_notification_user snu ON sn.id = snu.system_notification_id AND snu.user_id = p.user_id").
		Where("p.data_type = ?", model.ProfessionalDataTypeApproved).
		Where("p.ahpra_expiry_date <= ?", thirtyDaysLater.Format(xtool.DateDayA)).
		Where("p.ahpra_expiry_date >= ?", nowTime.Format(xtool.DateDayA)).
		Where("snu.id IS NULL"). // 排除已經發送過通知的
		Limit(CheckProfessionalDocumentExpiryMaxProcessRecordsPerRun)

	if err := builder.Scan(&professionals).Error; err != nil {
		logger.Errorf("[CRON] fail to get AHPRA expiring documents: %v", err)
		return nil
	}

	// 準備 AHPRA 證書到期通知請求
	var reqs []services.CreateProfessionalProfileDocumentExpireReq
	for _, prof := range professionals {
		documentType := model.ProfessionalProfileFileNameMap[model.ProfessionalFileCodeAhpraCertificate]
		documentTypeStr := xi18n.LocalizeWithLang("en", &documentType)
		reqs = append(reqs, services.CreateProfessionalProfileDocumentExpireReq{
			ProfessionalUserId: prof.UserId,
			ProfessionalId:     prof.ProfessionalId, // 使用 professional_id 作為關聯ID
			DocumentType:       documentTypeStr,
			ExpiryDate:         prof.AhpraExpiryDate.String(),
		})
	}

	logger.Infof("Found %d AHPRA certificates expiring within 30 days", len(reqs))
	return reqs
}

// 獲取身份檢查文件到期通知請求
func getIdCheckExpiryNotifications(db *gorm.DB, nowTime, thirtyDaysLater time.Time, logger *log.Entry) []services.CreateProfessionalProfileDocumentExpireReq {
	var professionals []struct {
		ProfessionalId    uint64         `json:"professionalId"`
		UserId            uint64         `json:"userId"`
		IdCheckExpiryDate xtype.NullDate `json:"idCheckExpiryDate"`
		ProfileJson       string         `json:"profileJson"`
	}

	// 查詢身份檢查文件即將到期的專業人士（排除已發送通知的）
	// professional_id + SystemNotificationRelatedTypeProfessionalFile 作為唯一key，避免重複發送通知
	builder := db.Table("professional AS p").
		Select([]string{
			"p.id AS professional_id",
			"p.user_id",
			"p.id_check_expiry_date",
			"p.profile_json",
		}).
		Joins("LEFT JOIN system_notification sn ON sn.related_id = p.id AND sn.notification_type = ? AND sn.related_type = ?",
			model.SystemNotificationTypeProfessionalProfileDocumentExpire, model.SystemNotificationRelatedTypeProfessionalFile).
		Joins("LEFT JOIN system_notification_user snu ON sn.id = snu.system_notification_id AND snu.user_id = p.user_id").
		Where("p.data_type = ?", model.ProfessionalDataTypeApproved).
		Where("p.id_check_expiry_date <= ?", thirtyDaysLater.Format(xtool.DateDayA)).
		Where("p.id_check_expiry_date >= ?", nowTime.Format(xtool.DateDayA)).
		Where("snu.id IS NULL"). // 排除已經發送過通知的
		Limit(CheckProfessionalDocumentExpiryMaxProcessRecordsPerRun)

	if err := builder.Scan(&professionals).Error; err != nil {
		logger.Errorf("[CRON] fail to get ID check expiring documents: %v", err)
		return nil
	}

	// 準備身份檢查文件到期通知請求
	var reqs []services.CreateProfessionalProfileDocumentExpireReq
	for _, prof := range professionals {
		if prof.ProfileJson == "" {
			continue
		}
		expiryDate := prof.IdCheckExpiryDate.String()

		// 解析 ProfileJson
		var profile model.ProfessionalProfile
		if err := json.Unmarshal([]byte(prof.ProfileJson), &profile); err != nil {
			logger.Errorf("[CRON] fail to unmarshal profile json for user %d: %v", prof.UserId, err)
			continue
		}

		// 遍歷 Files，找到對應到期日期的 FileCode
		for _, file := range profile.Files {
			if file.ExpiryDate == expiryDate {
				// 檢查該 FileCode 是否需要到期日期
				if needParam, exists := model.ProfessionalProfileFileNeedParamMap[file.FileCode]; exists {
					if needParam&model.ProfessionalProfileFileNeedExpiryDate != 0 {
						// 獲取文件名稱
						if documentName, exists := model.ProfessionalProfileFileNameMap[file.FileCode]; exists {
							documentNameStr := xi18n.LocalizeWithLang("en", &documentName)
							reqs = append(reqs, services.CreateProfessionalProfileDocumentExpireReq{
								ProfessionalUserId: prof.UserId,
								ProfessionalId:     prof.ProfessionalId,
								DocumentType:       documentNameStr,
								ExpiryDate:         file.ExpiryDate,
							})
							break
						}
					}
				}
			}
		}
	}

	logger.Infof("Found %d ID check documents expiring within 30 days", len(reqs))
	return reqs
}
