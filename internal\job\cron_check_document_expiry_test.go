package job

import (
	"testing"
	"time"

	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
)

// 測試防重複機制
func TestJobCheckProfessionalDocumentExpiry(t *testing.T) {
	db := setupTestEnvironment(t)

	traceId := uuid.NewV4().String()
	logger := log.WithField("traceId", traceId).WithField("task", CronCheckProfessionalDocumentExpiry)

	//nowTime := time.Now().UTC().Truncate(time.Second)
	nowTime := time.Date(2025, 9, 1, 0, 0, 0, 0, time.UTC)
	thirtyDaysLater := nowTime.AddDate(0, 0, 30)

	t.Run("ProfessionalDocumentExpiry Test", func(t *testing.T) {
		// 第一次執行
		checkExpiringDocuments(db, nowTime, thirtyDaysLater, logger)
		//time.Sleep(1 * time.Hour)
		t.Log("ProfessionalDocumentExpiry completed")
	})
}
