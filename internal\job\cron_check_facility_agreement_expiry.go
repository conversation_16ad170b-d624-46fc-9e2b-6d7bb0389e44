package job

import (
	"context"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

const (
	CronCheckFacilityAgreementExpiry = "cron_check_facility_agreement_expiry" // 檢查機構協議到期
)

// 檢查機構協議到期定時任務 - 每天0點執行
func jobCheckFacilityAgreementExpiry() {
	traceId := uuid.NewV4().String()
	ctx := context.Background()
	ctx = context.WithValue(ctx, "traceId", traceId)

	logger := log.WithField("traceId", traceId).WithField("task", CronCheckFacilityAgreementExpiry)

	db := xgorm.DB.WithContext(ctx)
	run, _, err := services.CronSettingService.CheckCronRunning(db, CronCheckFacilityAgreementExpiry)
	if err != nil {
		logger.Errorf("[CRON] fail to check facility agreement expiry task: %v", err)
		return
	}
	if !run {
		logger.Warnf("[CRON] <%s> cron job not run ", CronCheckFacilityAgreementExpiry)
		return
	}

	nowTime := time.Now().UTC()
	thirtyDaysLater := nowTime.AddDate(0, 0, 30)

	// 檢查即將到期的協議（30天內到期且沒有新協議）
	checkExpiringAgreements(db, nowTime, thirtyDaysLater, logger)

	logger.Info("facility agreement expiry check completed")
}

// 檢查即將到期的協議
func checkExpiringAgreements(db *gorm.DB, nowTime, thirtyDaysLater time.Time, logger *log.Entry) {
	nowStr := nowTime.Format(xtool.DateDayA)
	thirtyDaysLaterStr := thirtyDaysLater.Format(xtool.DateDayA)

	// 使用一個 SQL 查詢找到即將到期且沒有新協議的機構
	var facilitiesNeedingReminder []struct {
		FacilityId   uint64 `json:"facilityId"`
		FacilityName string `json:"facilityName"`
		EndTime      string `json:"endTime"`
		Email        string `json:"email"`
	}

	// 複雜查詢：找到即將到期的協議，且該機構沒有更新的協議
	builder := db.Table("facility_agreement AS fa").
		Joins("JOIN facility AS f ON f.id = fa.facility_id").
		Joins("JOIN facility_profile AS fp ON fp.facility_id = f.id AND fp.data_type = ?", model.FacilityProfileDataTypeApproved).
		Joins(`LEFT JOIN facility_agreement AS fa_new ON fa_new.facility_id = fa.facility_id AND fa_new.end_time > fa.end_time AND fa_new.status = ?`,
			model.FacilityAgreementStatusSigned).
		Select([]string{
			"fa.facility_id",
			"fp.name AS facility_name",
			"fa.end_time",
			"fp.email",
		}).
		Where("fa.status = ?", model.FacilityAgreementStatusSigned).
		Where("fa.end_time <= ?", thirtyDaysLaterStr).
		Where("fa.end_time > ?", nowStr).
		Where("fa_new.id IS NULL") // 沒有新協議

	if err := builder.Scan(&facilitiesNeedingReminder).Error; err != nil {
		logger.Errorf("[CRON] fail to get facilities needing renewal reminder: %v", err)
		return
	}
	if len(facilitiesNeedingReminder) == 0 {
		logger.Info("No facilities needing renewal reminder found")
		return
	}

	logger.Infof("Found %d facilities needing renewal reminder", len(facilitiesNeedingReminder))

	// 獲取系統管理員列表
	adminUsers, err := services.SystemNotificationService.GetSystemAdminUsers(db)
	if err != nil {
		logger.Errorf("[CRON] fail to get system admin users: %v", err)
		return
	}

	if len(adminUsers) == 0 {
		logger.Warn("[CRON] no system admin users found")
		return
	}
	adminEmails := make([]string, len(adminUsers))
	for i, admin := range adminUsers {
		adminEmails[i] = admin.Email
	}

	reminders := make([]services.AdminReminderInfo, 0)
	for _, facility := range facilitiesNeedingReminder {
		// 構建提醒信息
		reminderInfo := services.AdminReminderInfo{
			FacilityId:   facility.FacilityId,
			FacilityName: facility.FacilityName,
			AdminEmail:   adminEmails, // 發送給系統管理員
			Lang:         "en",        // 預設英文
		}

		reminders = append(reminders, reminderInfo)
	}
	// 發送續期提醒電郵
	if err = services.EmailNotificationService.SendAgreementRenewalReminderEmailBatch(reminders); err != nil {
		logger.WithError(err).Error("Failed to send agreement renewal reminder email to admin")
		return
	}
	logger.WithFields(log.Fields{
		"totalCount": len(facilitiesNeedingReminder),
	}).Info("Agreement renewal reminder processing completed")
}
