package job

import (
	"testing"
	"time"

	log "github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
)

// 測試完整的定時任務流程
func TestJobCheckFacilityAgreementExpiry(t *testing.T) {
	// 注意：這個測試需要確保 CronSetting 中有對應的配置
	// 並且 EmailNotificationService 的相關功能正常工作
	db := setupTestEnvironment(t)

	t.Run("JobExecution", func(t *testing.T) {
		logger := log.WithField("task", CronCheckFacilityAgreementExpiry)

		// 由於這是集成測試，我們只驗證函數不會 panic
		assert.NotPanics(t, func() {
			// 模擬指定時間
			nowTime := time.Date(2025, 7, 17, 0, 0, 0, 0, time.UTC)
			thirtyDaysLater := nowTime.AddDate(0, 0, 30)

			// 檢查即將到期的協議（30天內到期且沒有新協議）
			checkExpiringAgreements(db, nowTime, thirtyDaysLater, logger)
		}, "Job function should not panic")
	})
}
