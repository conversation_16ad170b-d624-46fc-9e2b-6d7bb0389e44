package job

import (
	"context"
	"fmt"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xgorm"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

const (
	CronCheckJobReminders                    = "cron_check_job_reminders"         // 檢查工作提醒
	CronCheckOngoingJobReminders             = "cron_check_ongoing_job_reminders" // 檢查多班次長期工作的定期提醒
	CheckJobRemindersMaxProcessRecordsPerRun = 100                                // 每次處理的最大記錄數
	CheckJobRemindersLockTimeoutSeconds      = 50                                 // 鎖定超時時間（秒）
)

type FacilityJobConfirmationInfo struct {
	JobId      uint64
	FacilityId uint64
	UserId     uint64
	BeginTime  time.Time
}

type JobCompletionInfo struct {
	JobId              uint64
	JobApplicationId   uint64
	NotificationUserId uint64
	FacilityId         uint64
	EndTime            time.Time
}

// 檢查工作提醒定時任務 - 每分鐘執行
func jobCheckJobReminders() {
	traceId := uuid.NewV4().String()
	ctx := context.Background()
	ctx = context.WithValue(ctx, "traceId", traceId)

	logger := log.WithField("traceId", traceId).WithField("task", CronCheckJobReminders)

	db := xgorm.DB.WithContext(ctx)
	run, _, err := services.CronSettingService.CheckCronRunning(db, CronCheckJobReminders)
	if err != nil {
		logger.Errorf("[CRON] fail to check job reminders task: %v", err)
		return
	}
	if !run {
		logger.Warnf("[CRON] <%s> cron job not run ", CronCheckJobReminders)
		return
	}

	nowTime := time.Now().UTC().Truncate(time.Second)

	// 檢查24小時提醒 - Professional和Facility
	check24HourReminders(db, nowTime, logger)

	// 檢查2小時提醒 - Professional
	check2HourReminders(db, nowTime, logger)

	// 檢查工作開始前2小時無人申請 - Facility
	checkNoApplication2Hour(db, nowTime, logger)

	// 檢查工作開始前1小時無人申請結束招聘 - Facility
	checkJobClosedForApplications1Hour(db, nowTime, logger)

	// 檢查距離工作開始24小時前還未確認好人選的工作
	checkUnconfirmedJobs24Hours(db, nowTime, logger)

	// 檢查工作完成後的確認單生成提醒
	checkJobCompletionConfirmationNote(db, nowTime, logger)

	logger.Info("job reminders check completed")
}

// 檢查多班次長期工作的定期提醒 - 每日0點執行
func jobCheckOngoingJobReminders() {
	traceId := uuid.NewV4().String()
	ctx := context.Background()
	ctx = context.WithValue(ctx, "traceId", traceId)

	logger := log.WithField("traceId", traceId).WithField("task", CronCheckOngoingJobReminders)

	db := xgorm.DB.WithContext(ctx)
	run, _, err := services.CronSettingService.CheckCronRunning(db, CronCheckOngoingJobReminders)
	if err != nil {
		logger.Errorf("[CRON] fail to check ongoing job reminders task: %v", err)
		return
	}
	if !run {
		logger.Warnf("[CRON] <%s> cron job not run ", CronCheckOngoingJobReminders)
		return
	}

	nowTime := time.Now().UTC().Truncate(time.Second)

	// 檢查多班次長期工作的定期提醒
	checkOngoingJobReminders(db, nowTime, logger)
}

// 檢查24小時提醒
func check24HourReminders(db *gorm.DB, nowTime time.Time, logger *log.Entry) {
	// 查詢24小時後開始的工作
	var jobReminders []services.JobReminderInfo

	// Professional 24小時提醒 - 查詢已接受的工作申請（排除已發送通知的）
	builder := db.Table("job AS j").
		Joins("JOIN job_application AS ja ON ja.job_id = j.id").
		Joins("LEFT JOIN system_notification sn ON sn.related_id = j.id AND sn.notification_type = ? AND sn.related_type = ?",
			model.SystemNotificationTypeProfessionalCalendar24Hour, model.SystemNotificationRelatedTypeJob).
		Joins("LEFT JOIN system_notification_user snu ON sn.id = snu.system_notification_id AND snu.user_id = ja.user_id").
		Select([]string{
			"j.id AS job_id",
			"ja.id AS job_application_id",
			"j.facility_id",
			"j.begin_time",
			"ja.user_id AS notification_user_id",
			fmt.Sprintf("'%s' AS reminder_type", services.ReminderTypeProfessional24Hour),
		}).
		Where("j.status = ?", model.JobStatusPublish).
		Where("ja.status = ?", model.JobApplicationStatusAccept).
		Where("ja.accept = ?", model.JobApplicationAcceptY).
		Where("j.begin_time > ?", nowTime).
		Where("j.begin_time <= ?", nowTime.Add(24*time.Hour)).
		Where("snu.id IS NULL"). // 排除已經發送過通知的
		Limit(CheckJobRemindersMaxProcessRecordsPerRun)

	if err := builder.Scan(&jobReminders).Error; err != nil {
		logger.Errorf("[CRON] fail to get 24 hour professional reminders: %v", err)
		return
	}

	// 在事務中批量創建專業人員24小時提醒通知
	if len(jobReminders) > 0 {
		err := db.Transaction(func(tx *gorm.DB) error {
			return services.SystemNotificationService.CreateProfessionalCalendar24HourBatch(tx, jobReminders)
		})
		if err != nil {
			logger.Errorf("[CRON] fail to send 24 hour reminders to professionals: %v", err)
		} else {
			logger.Infof("Successfully sent 24 hour reminders to %d professionals", len(jobReminders))
		}
	}

	// Facility 24小時提醒 - 查詢有已接受申請的工作（排除已發送通知的）
	var facilityReminders []services.JobReminderInfo
	facilityBuilder := db.Table("job AS j").
		Select([]string{
			"j.id AS job_id",
			"ja.id AS job_application_id", // 記錄已接受的申請ID
			"j.facility_id",
			"j.begin_time",
			"0 AS notification_user_id", // 不再需要特定用戶ID
			fmt.Sprintf("'%s' AS reminder_type", services.ReminderTypeFacility24Hour),
		}).
		Joins("JOIN job_application AS ja ON ja.job_id = j.id AND ja.status = ? AND ja.accept = ?", model.JobApplicationStatusAccept, model.JobApplicationAcceptY).
		Joins("LEFT JOIN system_notification sn ON sn.related_id = j.id AND sn.notification_type = ? AND sn.related_type = ?",
			model.SystemNotificationTypeFacilityCalendar24Hour, model.SystemNotificationRelatedTypeJob).
		Where("j.status = ?", model.JobStatusPublish).
		Where("j.begin_time > ?", nowTime).
		Where("j.begin_time <= ?", nowTime.Add(24*time.Hour)).
		Where("sn.id IS NULL"). // 排除已經發送過通知的工作
		Group("j.id, j.facility_id, j.begin_time").
		Limit(CheckJobRemindersMaxProcessRecordsPerRun)

	if err := facilityBuilder.Scan(&facilityReminders).Error; err != nil {
		logger.Errorf("[CRON] fail to get 24 hour facility reminders: %v", err)
		return
	}

	// 直接發送通知，不修改工作狀態
	if len(facilityReminders) > 0 {
		successCount := 0
		errorCount := 0

		for _, reminder := range facilityReminders {
			// 為每個提醒使用獨立事務
			err := db.Transaction(func(tx *gorm.DB) error {
				req := services.CreateFacilityCalendar24HourReq{
					JobId:     reminder.JobId,
					StartTime: reminder.BeginTime.Format(time.RFC3339),
				}
				return services.SystemNotificationService.CreateFacilityCalendar24Hour(tx, req)
			})

			if err != nil {
				logger.Errorf("[CRON] fail to send 24 hour facility notification for job %d: %v", reminder.JobId, err)
				errorCount++
			} else {
				successCount++
			}
		}

		logger.Infof("Successfully sent %d facility 24 hour notifications, %d failed", successCount, errorCount)
	}
}

// 檢查2小時提醒 - Professional
func check2HourReminders(db *gorm.DB, nowTime time.Time, logger *log.Entry) {
	var jobReminders []services.JobReminderInfo
	builder := db.Table("job AS j").
		Select([]string{
			"j.id AS job_id",
			"ja.id AS job_application_id",
			"j.facility_id",
			"j.begin_time",
			"ja.user_id AS notification_user_id",
			fmt.Sprintf("'%s' AS reminder_type", services.ReminderTypeProfessional2Hour),
		}).
		Joins("JOIN job_application AS ja ON ja.job_id = j.id").
		Joins("LEFT JOIN system_notification sn ON sn.related_id = j.id AND sn.notification_type = ? AND sn.related_type = ?",
			model.SystemNotificationTypeProfessionalCalendar2Hour, model.SystemNotificationRelatedTypeJob).
		Joins("LEFT JOIN system_notification_user snu ON sn.id = snu.system_notification_id AND snu.user_id = ja.user_id").
		Where("j.status = ?", model.JobStatusPublish).
		Where("ja.status = ?", model.JobApplicationStatusAccept).
		Where("ja.accept = ?", model.JobApplicationAcceptY).
		Where("j.begin_time > ?", nowTime).
		Where("j.begin_time <= ?", nowTime.Add(2*time.Hour)).
		Where("snu.id IS NULL"). // 排除已經發送過通知的
		Limit(CheckJobRemindersMaxProcessRecordsPerRun)

	if err := builder.Scan(&jobReminders).Error; err != nil {
		logger.Errorf("[CRON] fail to get 2 hour professional reminders: %v", err)
		return
	}

	// 在事務中批量創建專業人員2小時提醒通知
	if len(jobReminders) > 0 {
		err := db.Transaction(func(tx *gorm.DB) error {
			return services.SystemNotificationService.CreateProfessionalCalendar2HourBatch(tx, jobReminders)
		})
		if err != nil {
			logger.Errorf("[CRON] fail to send 2 hour reminders to professionals: %v", err)
		} else {
			logger.Infof("Successfully sent 2 hour reminders to %d professionals", len(jobReminders))
		}
	}
}

// 檢查工作開始前2小時無人申請
func checkNoApplication2Hour(db *gorm.DB, nowTime time.Time, logger *log.Entry) {
	var jobReminders []services.JobReminderInfo
	builder := db.Table("job AS j").
		Select([]string{
			"j.id AS job_id",
			"0 AS job_application_id", // 無申請提醒不需要具體的申請ID
			"j.facility_id",
			"j.begin_time",
			"0 AS notification_user_id", // 不再需要特定用戶ID
			fmt.Sprintf("'%s' AS reminder_type", services.ReminderTypeNoApplication2Hour),
		}).
		Joins("LEFT JOIN job_application AS ja ON ja.job_id = j.id").
		Joins("LEFT JOIN system_notification sn ON sn.related_id = j.id AND sn.notification_type = ? AND sn.related_type = ?",
			model.SystemNotificationTypeFacilityJobNoApplication, model.SystemNotificationRelatedTypeJob).
		Where("j.status = ?", model.JobStatusPublish).
		Where("j.begin_time > ?", nowTime).
		Where("j.begin_time <= ?", nowTime.Add(2*time.Hour)).
		Where("ja.id IS NULL"). // 無申請記錄
		Where("sn.id IS NULL"). // 排除已經發送過通知的工作
		Limit(CheckJobRemindersMaxProcessRecordsPerRun)

	if err := builder.Scan(&jobReminders).Error; err != nil {
		logger.Errorf("[CRON] fail to get no application 2 hour reminders: %v", err)
		return
	}

	// 直接發送通知，不修改工作狀態
	if len(jobReminders) > 0 {
		successCount := 0
		errorCount := 0

		for _, reminder := range jobReminders {
			// 為每個提醒使用獨立事務
			err := db.Transaction(func(tx *gorm.DB) error {
				req := services.CreateFacilityJobNoApplicationReq{
					JobId:     reminder.JobId,
					StartTime: reminder.BeginTime.Format(time.RFC3339),
				}
				return services.SystemNotificationService.CreateFacilityJobNoApplication(tx, req)
			})

			if err != nil {
				logger.Errorf("[CRON] fail to send no application notification for job %d: %v", reminder.JobId, err)
				errorCount++
			} else {
				successCount++
			}
		}

		logger.Infof("Successfully sent %d no application notifications, %d failed", successCount, errorCount)
	}
}

// 檢查距離工作開始24小時前還未確認好人選的工作
func checkUnconfirmedJobs24Hours(db *gorm.DB, nowTime time.Time, logger *log.Entry) {
	var jobReminders []services.JobReminderInfo
	// 查詢24小時後開始但還未確認人選的工作
	// 條件：有申請但沒有被接受的申請（排除已發送通知的）
	builder := db.Table("job AS j").
		Select([]string{
			"j.id AS job_id",
			"0 AS job_application_id", // 確認提醒不需要具體的申請ID
			"j.facility_id",
			"j.begin_time",
			"0 AS notification_user_id", // 不再需要特定用戶ID
			fmt.Sprintf("'%s' AS reminder_type", services.ReminderTypeFacilityJobNeedConfirm),
		}).
		Joins("JOIN job_application AS ja ON ja.job_id = j.id").
		Joins("LEFT JOIN job_application AS ja_accepted ON ja_accepted.job_id = j.id AND ja_accepted.status = ? AND ja_accepted.accept = ?",
			model.JobApplicationStatusAccept, model.JobApplicationAcceptY).
		Joins("LEFT JOIN system_notification sn ON sn.related_id = j.id AND sn.notification_type = ? AND sn.related_type = ?",
			model.SystemNotificationTypeFacilityJobNeedConfirm, model.SystemNotificationRelatedTypeJob).
		Where("j.status = ?", model.JobStatusPublish).
		Where("j.begin_time > ?", nowTime).
		Where("j.begin_time <= ?", nowTime.Add(24*time.Hour)).
		Where("ja_accepted.id IS NULL"). // 沒有被接受的申請
		Where("sn.id IS NULL").          // 排除已經發送過通知的工作
		Group("j.id, j.facility_id, j.begin_time").
		Having("COUNT(DISTINCT ja.id) > 0"). // 但有申請記錄
		Limit(CheckJobRemindersMaxProcessRecordsPerRun)

	if err := builder.Scan(&jobReminders).Error; err != nil {
		logger.Errorf("[CRON] fail to get unconfirmed jobs 24 hours: %v", err)
		return
	}

	// 直接發送通知，不修改工作狀態
	if len(jobReminders) > 0 {
		successCount := 0
		errorCount := 0

		for _, reminder := range jobReminders {
			// 為每個提醒使用獨立事務
			err := db.Transaction(func(tx *gorm.DB) error {
				req := services.CreateFacilityJobNeedConfirmReq{
					FacilityId:        reminder.FacilityId,
					JobId:             reminder.JobId,
					StartTime:         reminder.BeginTime.Format(time.RFC3339),
					ApplicationsCount: 0, // 這裡可以根據需要查詢實際申請數量
				}
				return services.SystemNotificationService.CreateFacilityJobNeedConfirm(tx, req)
			})

			if err != nil {
				logger.Errorf("[CRON] fail to send job need confirm notification for job %d: %v", reminder.JobId, err)
				errorCount++
			} else {
				successCount++
			}
		}

		logger.Infof("Successfully sent %d job need confirm notifications, %d failed", successCount, errorCount)
	}
}

// 檢查工作完成後的確認單生成提醒
func checkJobCompletionConfirmationNote(db *gorm.DB, nowTime time.Time, logger *log.Entry) {
	// 情況一：檢查整個工作已完成的情況
	checkCompletedJobs(db, nowTime, logger)
}

// 情況一：檢查整個工作已完成的情況
func checkCompletedJobs(db *gorm.DB, nowTime time.Time, logger *log.Entry) {
	var completedJobs []JobCompletionInfo

	// 查詢上一分鐘結束的工作，且專業人士已接受但還未生成確認單（排除已發送通知的）
	oneMinuteAgo := nowTime.Add(-1 * time.Minute)
	builder := db.Table("job AS j").
		Select([]string{
			"j.id AS job_id",
			"ja.id AS job_application_id",
			"ja.user_id AS notification_user_id",
			"j.facility_id",
			"j.end_time",
		}).
		Joins("JOIN job_application AS ja ON ja.job_id = j.id").
		Joins("LEFT JOIN document AS d ON d.job_application_id = ja.id AND d.category = ?", model.DocumentCategoryConfirmation).
		Joins("LEFT JOIN system_notification sn ON sn.related_id = ja.id AND sn.notification_type = ? AND sn.related_type = ?",
			model.SystemNotificationTypeProfessionalBillingConfirmationNoteGenerate, model.SystemNotificationRelatedTypeJobApplication).
		Joins("LEFT JOIN system_notification_user snu ON sn.id = snu.system_notification_id AND snu.user_id = ja.user_id").
		Where("j.status = ?", model.JobStatusPublish).
		Where("ja.status = ?", model.JobApplicationStatusAccept).
		Where("ja.accept = ?", model.JobApplicationAcceptY).
		Where("j.begin_time < ?", nowTime).
		Where("j.end_time <= ?", oneMinuteAgo).
		Where("d.id IS NULL").   // 還未生成確認單
		Where("snu.id IS NULL"). // 排除已經發送過通知的
		Limit(CheckJobRemindersMaxProcessRecordsPerRun)

	if err := builder.Scan(&completedJobs).Error; err != nil {
		logger.WithError(err).Error("Failed to get completed jobs for confirmation note reminder")
		return
	}

	if len(completedJobs) == 0 {
		logger.Debug("No completed jobs found for confirmation note reminder")
		return
	}

	var jobReminders []services.JobReminderInfo
	for _, job := range completedJobs {
		jobReminders = append(jobReminders, services.JobReminderInfo{
			JobId:              job.JobId,
			JobApplicationId:   job.JobApplicationId,
			FacilityId:         job.FacilityId,
			BeginTime:          job.EndTime,
			NotificationUserId: job.NotificationUserId,
			ReminderType:       services.ReminderTypeJobCompletionProfessional,
		})
	}

	logger.WithField("jobCount", len(jobReminders)).Info("Processing completed job confirmation note reminders")

	err := db.Transaction(func(tx *gorm.DB) error {
		return services.SystemNotificationService.CreateProfessionalBillingConfirmationNoteGenerateBatch(tx, jobReminders)
	})

	if err != nil {
		logger.WithError(err).Error("Failed to send completed job confirmation note reminders to professionals")
	} else {
		logger.WithField("successCount", len(jobReminders)).Info("Successfully sent completed job confirmation note reminders to professionals")
	}
}

// 情況二：檢查多班次長期工作的定期提醒（批量查詢優化版本）
func checkOngoingJobReminders(db *gorm.DB, nowTime time.Time, logger *log.Entry) {
	// 第一步：查詢可能需要檢查的工作（優化篩選條件）
	var ongoingJobs []JobCompletionInfo

	// 篩選至少13天前開始的工作（包含14天前當天開始的所有工作）
	thirteenDaysAgo := nowTime.AddDate(0, 0, -13)

	builder := db.Table("job AS j").
		Select([]string{
			"j.id AS job_id",
			"ja.id AS job_application_id",
			"ja.user_id AS notification_user_id",
			"j.facility_id",
			"j.begin_time AS end_time",
		}).
		Joins("JOIN job_application AS ja ON ja.job_id = j.id").
		Joins("LEFT JOIN document AS d ON d.job_application_id = ja.id AND d.category = ?", model.DocumentCategoryConfirmation).
		Where("j.status = ?", model.JobStatusPublish).
		Where("ja.status = ?", model.JobApplicationStatusAccept).
		Where("ja.accept = ?", model.JobApplicationAcceptY).
		Where("j.begin_time < ?", nowTime).          // 工作已開始
		Where("j.begin_time <= ?", thirteenDaysAgo). // 至少13天前開始
		Where("j.end_time > ?", nowTime).            // 工作未完全結束
		Where("d.id IS NULL").                       // 還未生成確認單
		Limit(CheckJobRemindersMaxProcessRecordsPerRun)

	if err := builder.Scan(&ongoingJobs).Error; err != nil {
		logger.WithError(err).Error("Failed to get ongoing jobs for confirmation note reminder")
		return
	}

	if len(ongoingJobs) == 0 {
		logger.Debug("No ongoing jobs found for confirmation note reminder")
		return
	}

	// 批量查詢所有工作的通知記錄和JobShift
	jobIds := make([]uint64, len(ongoingJobs))
	userIds := make([]uint64, len(ongoingJobs))
	for i, job := range ongoingJobs {
		jobIds[i] = job.JobId
		userIds[i] = job.NotificationUserId
	}
	var existingNotifications []struct {
		JobId       uint64 `json:"job_id"`
		UserId      uint64 `json:"user_id"`
		CycleNumber int    `json:"cycle_number"`
	}

	db.Table("system_notification sn").
		Select("sn.related_id AS job_id, snu.user_id, CAST(JSON_EXTRACT(sn.metadata, '$.cycle_number') AS UNSIGNED) AS cycle_number").
		Joins("JOIN system_notification_user snu ON sn.id = snu.system_notification_id").
		Where("sn.related_id IN ?", jobIds).
		Where("snu.user_id IN ?", userIds).
		Where("sn.notification_type = ?", model.SystemNotificationTypeProfessionalBillingConfirmationNoteGenerate).
		Where("sn.related_type = ?", model.SystemNotificationRelatedTypeJob).
		Where("JSON_EXTRACT(sn.metadata, '$.reminder_type') = ?", "ONGOING_JOB_REMINDER").
		Scan(&existingNotifications)

	var jobShifts []struct {
		JobId     uint64    `json:"job_id"`
		BeginTime time.Time `json:"begin_time"`
		EndTime   time.Time `json:"end_time"`
	}

	db.Table("job_shift").
		Select("job_id, begin_time, end_time").
		Where("job_id IN ?", jobIds).
		Scan(&jobShifts)

	// 在記憶體中進行匹配和篩選
	var validJobReminders []services.JobReminderInfo

	// 建立快速查找 map
	notificationMap := make(map[string]bool)
	for _, notification := range existingNotifications {
		key := fmt.Sprintf("%d_%d_%d", notification.JobId, notification.UserId, notification.CycleNumber)
		notificationMap[key] = true
	}
	jobShiftMap := make(map[uint64][]struct {
		BeginTime time.Time
		EndTime   time.Time
	})
	for _, shift := range jobShifts {
		jobShiftMap[shift.JobId] = append(jobShiftMap[shift.JobId], struct {
			BeginTime time.Time
			EndTime   time.Time
		}{shift.BeginTime, shift.EndTime})
	}

	for _, job := range ongoingJobs {
		cycleNumber := calculateCurrentCycle(job.EndTime, nowTime) // EndTime 實際是 BeginTime
		if cycleNumber <= 0 {
			continue
		}

		if !isInCycleCheckWindow(job.EndTime, nowTime, cycleNumber) {
			continue
		}

		notificationKey := fmt.Sprintf("%d_%d_%d", job.JobId, job.NotificationUserId, cycleNumber)
		if notificationMap[notificationKey] {
			continue
		}

		if !hasJobShiftInCycleFromMap(jobShiftMap[job.JobId], job.EndTime, cycleNumber) {
			continue
		}
		validJobReminders = append(validJobReminders, services.JobReminderInfo{
			JobId:              job.JobId,
			JobApplicationId:   job.JobApplicationId,
			FacilityId:         job.FacilityId,
			BeginTime:          job.EndTime,
			NotificationUserId: job.NotificationUserId,
			ReminderType:       services.ReminderTypeJobCompletionProfessional,
		})
	}

	if len(validJobReminders) == 0 {
		logger.Debug("No valid ongoing jobs found for confirmation note reminder")
		return
	}

	logger.WithField("jobCount", len(validJobReminders)).Info("Processing ongoing job confirmation note reminders")

	// 處理批量通知
	successCount := 0
	errorCount := 0

	for _, reminder := range validJobReminders {
		cycleNumber := calculateCurrentCycle(reminder.BeginTime, nowTime)
		if err := services.SystemNotificationService.CreateOngoingJobReminderNotification(db, reminder, cycleNumber, nowTime); err != nil {
			logger.WithError(err).WithFields(log.Fields{
				"jobId":              reminder.JobId,
				"notificationUserId": reminder.NotificationUserId,
				"cycleNumber":        cycleNumber,
			}).Error("Failed to send ongoing job confirmation note reminder")
			errorCount++
		} else {
			successCount++
		}
	}

	logger.WithFields(log.Fields{
		"totalCount":   len(validJobReminders),
		"successCount": successCount,
		"errorCount":   errorCount,
	}).Info("Completed ongoing job confirmation note reminders processing")
}

// 計算當前週期數
func calculateCurrentCycle(jobBeginTime time.Time, nowTime time.Time) int {
	// 按日期計算天數差（不考慮具體時間）
	jobDate := time.Date(jobBeginTime.Year(), jobBeginTime.Month(), jobBeginTime.Day(), 0, 0, 0, 0, jobBeginTime.Location())
	nowDate := time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), 0, 0, 0, 0, nowTime.Location())
	daysSinceBegin := int(nowDate.Sub(jobDate).Hours() / 24)

	// 計算當前是第幾天（從1開始）
	currentDay := daysSinceBegin + 1

	// 第1個週期檢查日：第15天
	// 第2個週期檢查日：第29天
	// 第3個週期檢查日：第43天
	if currentDay < 15 {
		return 0
	}

	return (currentDay-15)/14 + 1
}

// 檢查是否在週期檢查窗口內
func isInCycleCheckWindow(jobBeginTime time.Time, nowTime time.Time, cycleNumber int) bool {
	// 計算該週期的實際開始時間
	actualCycleStartTime := jobBeginTime.AddDate(0, 0, 13+(cycleNumber-1)*14)

	// 計算檢查日期（實際週期開始時間的次日0點）
	checkDate := time.Date(actualCycleStartTime.Year(), actualCycleStartTime.Month(), actualCycleStartTime.Day(), 0, 0, 0, 0, time.UTC).AddDate(0, 0, 1)

	// 當前日期（0點）
	nowDate := time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), 0, 0, 0, 0, time.UTC)

	return nowDate.Equal(checkDate)
}

// 檢查該週期是否已發送通知
func hasNotificationForCycle(db *gorm.DB, jobId, userId uint64, cycleNumber int) bool {
	var count int64
	db.Table("system_notification sn").
		Joins("JOIN system_notification_user snu ON sn.id = snu.system_notification_id").
		Where("sn.related_id = ?", jobId).
		Where("sn.related_type = ?", model.SystemNotificationRelatedTypeJob).
		Where("sn.notification_type = ?", model.SystemNotificationTypeProfessionalBillingConfirmationNoteGenerate).
		Where("JSON_EXTRACT(sn.metadata, '$.reminder_type') = ?", "ONGOING_JOB_REMINDER").
		Where("JSON_EXTRACT(sn.metadata, '$.cycle_number') = ?", cycleNumber).
		Where("snu.user_id = ?", userId).
		Count(&count)

	return count > 0
}

// 檢查該週期是否有 JobShift
func hasJobShiftInCycleFromMap(shifts []struct {
	BeginTime time.Time
	EndTime   time.Time
}, jobBeginTime time.Time, cycleNumber int) bool {
	cycleStartTime := jobBeginTime.AddDate(0, 0, 13+(cycleNumber-1)*14)
	cycleEndTime := cycleStartTime.AddDate(0, 0, 14)

	for _, shift := range shifts {
		if (shift.BeginTime.Before(cycleEndTime) && shift.EndTime.After(cycleStartTime)) ||
			(shift.BeginTime.Equal(cycleStartTime) || shift.EndTime.Equal(cycleEndTime)) {
			return true
		}
	}

	return false
}

// 檢查工作開始前1小時無人申請結束招聘
func checkJobClosedForApplications1Hour(db *gorm.DB, nowTime time.Time, logger *log.Entry) {
	// 距離工作開始1小時前還未有人申請工作，結束招聘 - 通知Facility

	var jobReminders []services.JobReminderInfo
	builder := db.Table("job AS j").
		Select([]string{
			"j.id AS job_id",
			"0 AS job_application_id", // 結束招聘提醒不需要具體的申請ID
			"j.facility_id",
			"j.begin_time",
			"0 AS notification_user_id", // 不再需要特定用戶ID
			fmt.Sprintf("'%s' AS reminder_type", services.ReminderTypeClosedForApplications1Hour),
		}).
		Joins("LEFT JOIN job_application AS ja ON ja.job_id = j.id").
		Joins("LEFT JOIN system_notification sn ON sn.related_id = j.id AND sn.notification_type = ? AND sn.related_type = ?",
			model.SystemNotificationTypeFacilityJobClosedForApplications, model.SystemNotificationRelatedTypeJob).
		Where("j.status = ?", model.JobStatusPublish).
		Where("j.begin_time > ?", nowTime).
		Where("j.begin_time <= ?", nowTime.Add(1*time.Hour)).
		Where("ja.id IS NULL"). // 無申請記錄
		Where("sn.id IS NULL"). // 排除已經發送過通知的工作
		Limit(CheckJobRemindersMaxProcessRecordsPerRun)

	if err := builder.Scan(&jobReminders).Error; err != nil {
		logger.Errorf("[CRON] fail to get closed for applications 1 hour reminders: %v", err)
		return
	}

	if len(jobReminders) > 0 {
		successCount := 0
		errorCount := 0

		for _, reminder := range jobReminders {
			// 為每個提醒使用獨立事務
			err := db.Transaction(func(tx *gorm.DB) error {
				req := services.CreateFacilityJobClosedForApplicationsReq{
					JobId:     reminder.JobId,
					StartTime: reminder.BeginTime.Format(time.RFC3339),
				}
				return services.SystemNotificationService.CreateFacilityJobClosedForApplications(tx, req)
			})

			if err != nil {
				logger.Errorf("[CRON] fail to send closed for applications notification for job %d: %v", reminder.JobId, err)
				errorCount++
			} else {
				successCount++
			}
		}

		logger.Infof("Successfully sent %d closed for applications notifications, %d failed", successCount, errorCount)
	}
}
