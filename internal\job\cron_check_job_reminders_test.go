package job

import (
	"context"
	"github.com/Norray/medic-crew/internal/task"
	"testing"
	"time"

	"github.com/Norray/xrocket/xi18n"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xredis"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// 測試環境初始化
func setupTestEnvironment(t *testing.T) *gorm.DB {
	xconfig.Setup("../../config/app.ini")
	xredis.DefaultSetup()
	xgorm.DefaultSetup()
	task.SetupWorkers()
	xi18n.Setup([]string{
		"../../resource/i18n/active.en.toml",
		"../../resource/i18n/active.zh-CN.toml",
		"../../resource/i18n/active.zh-HK.toml",
	})

	traceId := uuid.NewV4().String()
	ctx := context.Background()
	ctx = context.WithValue(ctx, "traceId", traceId)

	return xgorm.DB.WithContext(ctx)
}

// 測試24小時提醒功能
func TestCheck24HourReminders(t *testing.T) {
	db := setupTestEnvironment(t)

	traceId := uuid.NewV4().String()
	logger := log.WithField("traceId", traceId).WithField("task", CronCheckJobReminders)

	// 使用當前時間進行測試
	nowTime := time.Now().UTC().Truncate(time.Second)

	t.Run("Professional 24 Hour Reminders", func(t *testing.T) {
		// 測試專業人員24小時提醒
		check24HourReminders(db, nowTime, logger)
		t.Log("Professional 24 hour reminders test completed")
	})

	t.Run("Facility 24 Hour Reminders", func(t *testing.T) {
		// 測試機構24小時提醒
		check24HourReminders(db, nowTime, logger)
		t.Log("Facility 24 hour reminders test completed")
	})
}

// 測試2小時提醒功能
func TestCheck2HourReminders(t *testing.T) {
	db := setupTestEnvironment(t)

	traceId := uuid.NewV4().String()
	logger := log.WithField("traceId", traceId).WithField("task", CronCheckJobReminders)

	// 使用當前時間進行測試
	nowTime := time.Now().UTC().Truncate(time.Second)

	t.Run("Professional 2 Hour Reminders", func(t *testing.T) {
		// 測試專業人員2小時提醒
		check2HourReminders(db, nowTime, logger)
		t.Log("Professional 2 hour reminders test completed")
	})
}

// 測試無申請2小時提醒功能
func TestCheckNoApplication2Hour(t *testing.T) {
	db := setupTestEnvironment(t)

	traceId := uuid.NewV4().String()
	logger := log.WithField("traceId", traceId).WithField("task", CronCheckJobReminders)

	// 使用當前時間進行測試
	nowTime := time.Now().UTC().Truncate(time.Second)

	t.Run("No Application 2 Hour Reminders", func(t *testing.T) {
		// 測試無申請2小時提醒
		checkNoApplication2Hour(db, nowTime, logger)
		t.Log("No application 2 hour reminders test completed")
	})
}

// 測試完整的工作提醒檢查流程
func TestJobCheckJobReminders(t *testing.T) {
	t.Run("Full Job Reminders Check", func(t *testing.T) {
		// 執行完整的工作提醒檢查
		jobCheckJobReminders()
		t.Log("Full job reminders check test completed")
	})
}

// 測試防重複機制
func TestDuplicatePreventionMechanism(t *testing.T) {
	db := setupTestEnvironment(t)

	traceId := uuid.NewV4().String()
	logger := log.WithField("traceId", traceId).WithField("task", CronCheckJobReminders)

	nowTime := time.Now().UTC().Truncate(time.Second)

	t.Run("Duplicate Prevention Test", func(t *testing.T) {
		// 第一次執行
		t.Log("First execution - should process reminders")
		check24HourReminders(db, nowTime, logger)

		// 第二次執行 - 應該跳過已處理的提醒
		t.Log("Second execution - should skip already processed reminders")
		check24HourReminders(db, nowTime, logger)

		t.Log("Duplicate prevention test completed")
	})
}

// 測試時間窗口邏輯
func TestTimeWindowLogic(t *testing.T) {
	db := setupTestEnvironment(t)

	traceId := uuid.NewV4().String()
	logger := log.WithField("traceId", traceId).WithField("task", CronCheckJobReminders)

	t.Run("24 Hour Window Test", func(t *testing.T) {
		// 測試24小時時間窗口
		testTime := time.Now().UTC().Add(24 * time.Hour)
		check24HourReminders(db, testTime, logger)
		t.Log("24 hour window test completed")
	})

	t.Run("2 Hour Window Test", func(t *testing.T) {
		// 測試2小時時間窗口
		testTime := time.Now().UTC().Add(2 * time.Hour)
		check2HourReminders(db, testTime, logger)
		checkNoApplication2Hour(db, testTime, logger)
		t.Log("2 hour window test completed")
	})
}

// 測試批量通知服務
func TestBatchNotificationServices(t *testing.T) {
	db := setupTestEnvironment(t)

	// 創建測試用的JobReminderInfo
	testReminders := []services.JobReminderInfo{
		{
			JobId:              1,
			JobApplicationId:   1,
			FacilityId:         1,
			BeginTime:          time.Now().UTC().Add(24 * time.Hour),
			NotificationUserId: 1,
			ReminderType:       services.ReminderTypeProfessional24Hour,
		},
	}

	t.Run("Professional 24 Hour Batch", func(t *testing.T) {
		err := db.Transaction(func(tx *gorm.DB) error {
			return services.SystemNotificationService.CreateProfessionalCalendar24HourBatch(tx, testReminders)
		})
		if err != nil {
			t.Logf("Professional 24 hour batch test completed with expected result: %v", err)
		} else {
			t.Log("Professional 24 hour batch test completed successfully")
		}
	})

	t.Run("Professional 2 Hour Batch", func(t *testing.T) {
		testReminders[0].ReminderType = services.ReminderTypeProfessional2Hour
		err := db.Transaction(func(tx *gorm.DB) error {
			return services.SystemNotificationService.CreateProfessionalCalendar2HourBatch(tx, testReminders)
		})
		if err != nil {
			t.Logf("Professional 2 hour batch test completed with expected result: %v", err)
		} else {
			t.Log("Professional 2 hour batch test completed successfully")
		}
	})

	t.Run("Facility 24 Hour Batch", func(t *testing.T) {
		testReminders[0].ReminderType = services.ReminderTypeFacility24Hour
		err := db.Transaction(func(tx *gorm.DB) error {
			return services.SystemNotificationService.CreateFacilityCalendar24HourBatch(tx, testReminders)
		})
		if err != nil {
			t.Logf("Facility 24 hour batch test completed with expected result: %v", err)
		} else {
			t.Log("Facility 24 hour batch test completed successfully")
		}
	})

	t.Run("No Application Batch", func(t *testing.T) {
		testReminders[0].ReminderType = services.ReminderTypeNoApplication2Hour
		testReminders[0].JobApplicationId = 0 // 無申請提醒不需要申請ID
		err := db.Transaction(func(tx *gorm.DB) error {
			return services.SystemNotificationService.CreateFacilityJobNoApplicationBatch(tx, testReminders)
		})
		if err != nil {
			t.Logf("No application batch test completed with expected result: %v", err)
		} else {
			t.Log("No application batch test completed successfully")
		}
	})

	t.Run("Auto Cancel Batch", func(t *testing.T) {
		testReminders[0].ReminderType = services.ReminderTypeClosedForApplications1Hour
		err := db.Transaction(func(tx *gorm.DB) error {
			return services.SystemNotificationService.CreateFacilityJobClosedForApplicationsBatch(tx, testReminders)
		})
		if err != nil {
			t.Logf("Auto cancel batch test completed with expected result: %v", err)
		} else {
			t.Log("Auto cancel batch test completed successfully")
		}
	})
}

// 測試錯誤處理
func TestErrorHandling(t *testing.T) {
	db := setupTestEnvironment(t)

	t.Run("Empty Reminders List", func(t *testing.T) {
		emptyReminders := []services.JobReminderInfo{}

		err := db.Transaction(func(tx *gorm.DB) error {
			return services.SystemNotificationService.CreateProfessionalCalendar24HourBatch(tx, emptyReminders)
		})

		if err != nil {
			t.Errorf("Empty reminders should not cause error: %v", err)
		} else {
			t.Log("Empty reminders test passed")
		}
	})

	t.Run("Invalid Job Data", func(t *testing.T) {
		invalidReminders := []services.JobReminderInfo{
			{
				JobId:              0, // 無效的JobId
				JobApplicationId:   0,
				FacilityId:         0,
				BeginTime:          time.Time{}, // 零值時間
				NotificationUserId: 0,
				ReminderType:       services.ReminderTypeProfessional24Hour,
			},
		}

		err := db.Transaction(func(tx *gorm.DB) error {
			return services.SystemNotificationService.CreateProfessionalCalendar24HourBatch(tx, invalidReminders)
		})

		// 預期會有錯誤，因為數據無效
		if err != nil {
			t.Logf("Invalid data test completed with expected error: %v", err)
		} else {
			t.Log("Invalid data test completed without error")
		}
	})
}

// 測試工作完成確認單提醒功能
func TestCheckJobCompletionConfirmationNote(t *testing.T) {
	db := setupTestEnvironment(t)

	traceId := uuid.NewV4().String()
	logger := log.WithField("traceId", traceId).WithField("task", CronCheckJobReminders)

	// 使用當前時間進行測試
	nowTime := time.Now().UTC().Truncate(time.Second)

	t.Run("Completed Jobs Confirmation Note Reminders", func(t *testing.T) {
		// 測試已完成工作的確認單提醒
		checkJobCompletionConfirmationNote(db, nowTime, logger)
		t.Log("Completed jobs confirmation note reminders test completed")
	})

	t.Run("Different Time Windows", func(t *testing.T) {
		// 測試不同時間窗口
		testTimes := []time.Time{
			nowTime.Add(-1 * time.Minute),  // 1分鐘前
			nowTime.Add(-5 * time.Minute),  // 5分鐘前
			nowTime.Add(-10 * time.Minute), // 10分鐘前
		}

		for i, testTime := range testTimes {
			t.Logf("Testing time window %d: %v", i+1, testTime)
			checkJobCompletionConfirmationNote(db, testTime, logger)
		}
		t.Log("Different time windows test completed")
	})

	t.Run("Edge Cases", func(t *testing.T) {
		// 測試邊界情況
		// 測試整點時間
		exactHour := time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), nowTime.Hour(), 0, 0, 0, nowTime.Location())
		checkJobCompletionConfirmationNote(db, exactHour, logger)

		// 測試午夜時間
		midnight := time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), 0, 0, 0, 0, nowTime.Location())
		checkJobCompletionConfirmationNote(db, midnight, logger)

		t.Log("Edge cases test completed")
	})
}

// 測試多班次長期工作的定期提醒功能
func TestCheckOngoingJobReminders(t *testing.T) {
	db := setupTestEnvironment(t)

	traceId := uuid.NewV4().String()
	logger := log.WithField("traceId", traceId).WithField("task", CronCheckOngoingJobReminders)

	// 使用當前時間進行測試
	nowTime := time.Now().UTC().Truncate(time.Second)

	t.Run("Ongoing Job Reminders", func(t *testing.T) {
		// 測試多班次長期工作的定期提醒
		checkOngoingJobReminders(db, nowTime, logger)
		t.Log("Ongoing job reminders test completed")
	})

	t.Run("Different Cycle Scenarios", func(t *testing.T) {
		// 測試不同週期場景
		testDates := []time.Time{
			// 測試第1個週期（14天後）
			time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC), // 假設工作從1月1日開始
			// 測試第2個週期（28天後）
			time.Date(2024, 1, 29, 0, 0, 0, 0, time.UTC),
			// 測試第3個週期（42天後）
			time.Date(2024, 2, 12, 0, 0, 0, 0, time.UTC),
		}

		for i, testDate := range testDates {
			t.Logf("Testing cycle scenario %d: %v", i+1, testDate)
			checkOngoingJobReminders(db, testDate, logger)
		}
		t.Log("Different cycle scenarios test completed")
	})

	t.Run("Cycle Calculation Logic", func(t *testing.T) {
		// 測試週期計算邏輯
		jobBeginTime := time.Date(2024, 1, 1, 9, 0, 0, 0, time.UTC)

		testCases := []struct {
			currentTime   time.Time
			expectedCycle int
			description   string
		}{
			{
				currentTime:   time.Date(2024, 1, 10, 0, 0, 0, 0, time.UTC),
				expectedCycle: 0, // 還未到第一個週期
				description:   "Before first cycle",
			},
			{
				currentTime:   time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC),
				expectedCycle: 1, // 第一個週期
				description:   "First cycle",
			},
			{
				currentTime:   time.Date(2024, 1, 29, 0, 0, 0, 0, time.UTC),
				expectedCycle: 2, // 第二個週期
				description:   "Second cycle",
			},
			{
				currentTime:   time.Date(2024, 2, 12, 0, 0, 0, 0, time.UTC),
				expectedCycle: 3, // 第三個週期
				description:   "Third cycle",
			},
		}

		for _, tc := range testCases {
			cycle := calculateCurrentCycle(jobBeginTime, tc.currentTime)
			if cycle != tc.expectedCycle {
				t.Errorf("%s: expected cycle %d, got %d", tc.description, tc.expectedCycle, cycle)
			} else {
				t.Logf("%s: cycle %d calculated correctly", tc.description, cycle)
			}
		}
		t.Log("Cycle calculation logic test completed")
	})

	t.Run("Cycle Check Window Logic", func(t *testing.T) {
		// 測試週期檢查窗口邏輯
		jobBeginTime := time.Date(2024, 1, 1, 9, 0, 0, 0, time.UTC)

		testCases := []struct {
			currentTime time.Time
			cycleNumber int
			shouldMatch bool
			description string
		}{
			{
				currentTime: time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC),
				cycleNumber: 1,
				shouldMatch: true,
				description: "First cycle check date",
			},
			{
				currentTime: time.Date(2024, 1, 14, 0, 0, 0, 0, time.UTC),
				cycleNumber: 1,
				shouldMatch: false,
				description: "Day before first cycle check",
			},
			{
				currentTime: time.Date(2024, 1, 29, 0, 0, 0, 0, time.UTC),
				cycleNumber: 2,
				shouldMatch: true,
				description: "Second cycle check date",
			},
		}

		for _, tc := range testCases {
			inWindow := isInCycleCheckWindow(jobBeginTime, tc.currentTime, tc.cycleNumber)
			if inWindow != tc.shouldMatch {
				t.Errorf("%s: expected %v, got %v", tc.description, tc.shouldMatch, inWindow)
			} else {
				t.Logf("%s: window check %v is correct", tc.description, inWindow)
			}
		}
		t.Log("Cycle check window logic test completed")
	})
}

// 測試並發安全性
func TestConcurrencySafety(t *testing.T) {
	db := setupTestEnvironment(t)

	traceId := uuid.NewV4().String()
	logger := log.WithField("traceId", traceId).WithField("task", CronCheckJobReminders)

	nowTime := time.Now().UTC().Truncate(time.Second)

	t.Run("Concurrent Execution", func(t *testing.T) {
		// 模擬並發執行
		done := make(chan bool, 4)

		go func() {
			check24HourReminders(db, nowTime, logger)
			done <- true
		}()

		go func() {
			check2HourReminders(db, nowTime, logger)
			done <- true
		}()

		go func() {
			checkJobCompletionConfirmationNote(db, nowTime, logger)
			done <- true
		}()

		go func() {
			checkOngoingJobReminders(db, nowTime, logger)
			done <- true
		}()

		// 等待所有goroutine完成
		for i := 0; i < 4; i++ {
			<-done
		}

		t.Log("Concurrent execution test completed")
	})
}
