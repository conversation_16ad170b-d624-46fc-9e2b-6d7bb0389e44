package job

import (
	"context"
	"encoding/json"

	"github.com/Norray/xrocket/xamqp"

	"github.com/Norray/medic-crew/internal/task"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
)

const CronCheckSendPayUpfrontInvoice = "cron_check_send_pay_upfront_invoice" // 檢查需要發送預付發票的工作

// 生成發票草稿定時任務 - 每分鐘執行
func CheckSendPayUpfrontInvoice() {
	traceId := uuid.NewV4().String()
	ctx := context.Background()
	ctx = context.WithValue(ctx, "traceId", traceId)

	logger := log.With<PERSON>ield("traceId", traceId).WithField("task", CronCheckSendPayUpfrontInvoice)

	logger.Infof("Start %s task", CronCheckSendPayUpfrontInvoice)

	db := xgorm.DB.WithContext(ctx)

	// 檢查定時任務是否應該運行
	run, _, err := services.CronSettingService.CheckCronRunning(db, CronCheckSendPayUpfrontInvoice)
	if err != nil {
		logger.Errorf("[CRON] fail to check send pay upfront invoice task: %v", err)
		return
	}
	if !run {
		logger.Warnf("[CRON] <%s> cron job not run ", CronCheckSendPayUpfrontInvoice)
		return
	}
	today := xtool.NowFormat(xtool.DateDayA)

	// 查詢符合條件的發票ID：
	// 1. 發票狀態為草稿
	// 2. 發票日期在今天之前
	// 3. 發票類型為預付
	// 4. 發票數據類型為系統發給機構
	var documentIds []uint64
	err = db.Table("document as d").
		Select("DISTINCT d.id").
		Joins("JOIN job_application as ja ON d.job_application_id = ja.id").
		Joins("JOIN job as j ON ja.job_id = j.id").
		Where("d.progress = ?", model.DocumentProgressDraft).
		Where("j.payment_terms = ?", model.JobPaymentTermsPayUpfront).
		Where("d.data_type = ?", model.DocumentDataTypeSystemToFacility).
		Where("d.document_date <= ?", today).
		Limit(10). // 每次處理限制數量，避免一次處理太多
		Pluck("d.id", &documentIds).Error

	if err != nil {
		logger.Errorf("Fail to query documents: %v", err)
		return
	}

	logger.Infof("Found %d jobs need to generate invoice", len(documentIds))

	// 將每個符合條件的工作發送到隊列
	for _, documentId := range documentIds {
		req := task.SendPayUpfrontInvoiceReq{
			DocumentId: documentId,
		}
		var reqData []byte
		reqData, err = json.Marshal(req)
		if err != nil {
			logger.Errorf("Fail to marshal request: %v, documentId: %d", err, documentId)
			continue
		}
		// 發送到隊列
		err = xamqp.SendTask(task.SendPayUpfrontInvoiceTask, xamqp.Task{
			MessageId: task.SendPayUpfrontInvoiceTask,
			TaskId:    task.SendPayUpfrontInvoiceTask + "_" + uuid.NewV4().String(),
			Data:      string(reqData),
		})
		if err != nil {
			logger.Errorf("Fail to send task: %v, documentId: %d", err, documentId)
			continue
		}

		logger.Infof("Send send pay upfront invoice task for documentId: %d", documentId)
	}

	logger.Info("CheckSendPayUpfrontInvoice task completed")
}
