package job

import (
	"context"
	"encoding/json"
	"time"

	"github.com/Norray/medic-crew/internal/task"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xamqp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
)

const (
	CronJobNewAvailable = "cron_job_new_available" // 新工作可用通知
)

// 新工作可用通知定時任務 - 每分鐘執行
func jobNewAvailable() {
	traceId := uuid.NewV4().String()
	ctx := context.Background()
	ctx = context.WithValue(ctx, "traceId", traceId)

	logger := log.WithField("traceId", traceId).WithField("task", CronJobNewAvailable)

	db := xgorm.DB.WithContext(ctx)
	run, _, err := services.CronSettingService.CheckCronRunning(db, CronJobNewAvailable)
	if err != nil {
		logger.Errorf("[CRON] fail to check job new available task: %v", err)
		return
	}
	if !run {
		logger.Warnf("[CRON] <%s> cron job not run ", CronJobNewAvailable)
		return
	}

	nowTime := time.Now().UTC().Truncate(time.Second)
	lastMinute := nowTime.Add(-1 * time.Minute).Format(xtool.DateTimeA2) // 格式: 2006-01-02 15:04

	// 查找在上一分鐘發佈的工作
	var jobIds []uint64
	if err := db.Table("job").
		Select("id").
		Where("status = ?", model.JobStatusPublish).
		Where("DATE_FORMAT(publish_time, '%Y-%m-%d %H:%i') = ?", lastMinute).
		Find(&jobIds).Error; err != nil {
		logger.Errorf("[CRON] fail to get newly published jobs: %v", err)
		return
	}

	if len(jobIds) == 0 {
		logger.Info("No newly published jobs found")
		return
	}

	logger.Infof("Found %d newly published jobs", len(jobIds))

	// 提交到隊列
	for _, jobId := range jobIds {
		req := task.JobNewAvailableReq{
			JobId: jobId,
		}
		str, _ := json.Marshal(req)
		err = xamqp.SendTask(task.JobNewAvailableTask, xamqp.Task{
			MessageId: task.JobNewAvailableTask,
			TaskId:    task.JobNewAvailableTask + "_" + uuid.NewV4().String(),
			Data:      string(str),
		})
		if err != nil {
			logger.Errorf("can not send task: %s, JobId: %d", err, jobId)
		} else {
			logger.Infof("send task: %s, JobId: %d", task.JobNewAvailableTask, jobId)
		}
	}
}
