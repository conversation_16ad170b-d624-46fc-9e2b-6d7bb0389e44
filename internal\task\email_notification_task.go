package task

import (
	"context"
	"encoding/json"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xgorm"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
)

// 統一電郵任務常量
const EmailNotificationTask = "email_notification_task"

// 統一電郵任務請求結構
type EmailNotificationTaskReq struct {
	EmailType string      `json:"emailType"` // 電郵類型
	Data      interface{} `json:"data"`      // 電郵數據
}

// 統一電郵任務處理函數
func EmailNotification(taskJson string) (context.Context, error) {
	var err error
	ctx := context.Background()
	traceId := uuid.NewV4().String()
	ctx = context.WithValue(ctx, "traceId", traceId)

	logger := log.WithField("traceId", traceId).WithField("task", services.EmailNotificationTask)

	// 解析任務數據
	taskData := EmailNotificationTaskReq{}
	err = json.Unmarshal([]byte(taskJson), &taskData)
	if err != nil {
		logger.Errorf("fail to unmarshal task: %v, taskJson: %s", err, taskJson)
		return ctx, err
	}

	logger = logger.WithField("emailType", taskData.EmailType)
	logger.Info("Start to process email notification")

	// 獲取數據庫連接
	db := xgorm.DB.WithContext(ctx)

	// 根據電郵類型分發處理
	switch taskData.EmailType {
	// 管理員通知
	case services.EmailTypeAdminNewFacilityApplication:
		err = services.ProcessAdminNewFacilityApplication(db, taskData.Data)
	case services.EmailTypeAdminHelpDeskNotification:
		err = services.ProcessAdminHelpDeskNotification(db, taskData.Data)

	// 機構通知
	case services.EmailTypeFacilityAgreementReady:
		err = services.ProcessFacilityAgreementReady(db, taskData.Data)
	case services.EmailTypeFacilityAgreementRenewalReminder:
		err = services.ProcessFacilityAgreementRenewalReminder(db, taskData.Data)
	case services.EmailTypeFacilityAgreementExpired:
		err = services.ProcessFacilityAgreementExpired(db, taskData.Data)
	case services.EmailTypeFacilityProfileApproved:
		err = services.ProcessFacilityApproved(db, taskData.Data)
	case services.EmailTypeFacilityProfileRejected:
		err = services.ProcessFacilityRejected(db, taskData.Data)
	case services.EmailTypeFacilityInvoiceNotification:
		err = services.ProcessFacilityInvoiceNotification(db, taskData.Data)
	case services.EmailTypeFacilityUrgentShiftUnfilled:
		err = services.ProcessFacilityUrgentShiftUnfilled(db, taskData.Data)
	case services.EmailTypeFacilityJobCancellation:
		err = services.ProcessFacilityJobCancellation(db, taskData.Data)

	// 專業人士通知
	case services.EmailTypeProfessionalRejected:
		err = services.ProcessProfessionalRejected(db, taskData.Data)
	case services.EmailTypeProfessionalApproved:
		err = services.ProcessProfessionalApproved(db, taskData.Data)
	case services.EmailTypeProfessionalJobCancellation:
		err = services.ProcessProfessionalJobCancellation(db, taskData.Data)

	default:
		logger.Errorf("unknown email type: %s", taskData.EmailType)
		return ctx, nil // 不返回錯誤，避免任務重試
	}

	if err != nil {
		logger.Errorf("fail to process email notification: %v", err)
		return ctx, err
	}

	logger.Info("email notification task completed")
	return ctx, nil
}
