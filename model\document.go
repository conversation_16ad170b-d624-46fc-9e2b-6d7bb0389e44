package model

import (
	"time"

	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xmodel/xtype"
	"github.com/shopspring/decimal"
)

const (
	DocumentCategoryConfirmation = "CONFIRMATION" // 確認單
	DocumentCategoryInvoice      = "INVOICE"      // 發票
	DocumentCategoryCreditNote   = "CREDIT_NOTE"  // 貸記通知單

	DocumentDataTypeSystemToFacility       = "S2F" // 系統開立給機構
	DocumentDataTypeProfessionalToFacility = "P2F" // 專業人員開立給機構
	DocumentDataTypeProfessionalToSystem   = "P2S" // 專業人員開立給系統
	DocumentDataTypeSystemCompensation     = "S2P" // 系統開立給專業人員 (自動賠付)

	DocumentProgressDraft   = "DRAFT"   // 草稿
	DocumentProgressSent    = "SENT"    // 已送出(只有CONFIRMATION使用)
	DocumentProgressReject  = "REJECT"  // 已拒絕(只有CONFIRMATION使用)
	DocumentProgressConfirm = "CONFIRM" // 已確認
	DocumentProgressCancel  = "CANCEL"  // 已取消

	DocumentProgressPaymentReceivedY = "Y" // 已收到付款 (只有P2F 或者 P2S 的 INVOICE 使用)
	DocumentProgressPaymentReceivedN = "N" // 未收到付款 (只有P2F 或者 P2S 的 INVOICE 使用)

	DocumentPaidY = "Y" // 已付款
	DocumentPaidN = "N" // 未付款
)

// Document 單據
type Document struct {
	Id                    uint64               `json:"id" gorm:"primary_key"`
	FromDocumentId        uint64               `json:"fromDocumentId" gorm:"index:from_document_idx;not null"`                 // 來源單據ID (一般是CONFIRMATION的ID)
	Category              string               `json:"category" gorm:"type:varchar(32);unique_index:document_no_idx;not null"` // 單據類型
	JobApplicationId      uint64               `json:"jobApplicationId" gorm:"index:job_application_idx;not null"`             // 工作申請ID
	DataType              string               `json:"dataType" gorm:"index:invoice_type_idx;not null"`                        // 數據類型
	SeqNo                 int32                `json:"seqNo" gorm:"not null"`                                                  // 序號(同一個JobApplicationId，Category，DataType的第幾張單)
	DocumentDate          xtype.Date           `swaggertype:"string" json:"documentDate" gorm:"type:date;not null"`            // 單據日期
	DueDate               xtype.NullDate       `swaggertype:"string" json:"dueDate" gorm:"type:date"`                          // 到期日期 (可以為空)
	DocumentNo            xtype.JsonNullString `json:"documentNo" gorm:"type:varchar(255);unique_index:document_no_idx;"`      // 單據號碼 (可以為空)
	FacilityId            uint64               `json:"facilityId" gorm:"index:facility_idx;not null"`                          // 機構ID
	ProfessionalId        uint64               `json:"professionalId" gorm:"index:professional_idx;not null"`                  // 專業人員ID
	UserId                uint64               `json:"userId" gorm:"index:user_idx;not null"`                                  // 專業人員的用戶ID
	FromName              string               `json:"fromName" gorm:"type:varchar(255);not null"`                             // FROM 名稱
	FromEmail             string               `json:"fromEmail" gorm:"type:varchar(255);not null"`                            // FROM 郵件
	FromAddress           string               `json:"fromAddress" gorm:"type:varchar(255);not null"`                          // FROM 地址
	FromBankStateBranch   string               `json:"fromBankStateBranch" gorm:"type:varchar(255);not null"`                  // FROM的銀行分行(BSB)
	FromBankAccountNumber string               `json:"fromBankAccountNumber" gorm:"type:varchar(255);not null"`                // FROM的銀行帳戶號碼
	FromBankAccountName   string               `json:"fromBankAccountName" gorm:"type:varchar(255);not null"`                  // FROM的銀行帳戶名稱
	FromAbn               string               `json:"fromAbn" gorm:"type:varchar(255);not null"`                              // FROM ABN (澳洲商業號碼)
	ToName                string               `json:"toName" gorm:"type:varchar(255);not null"`                               // TO 名稱
	ToAbn                 string               `json:"toAbn" gorm:"type:varchar(255);not null"`                                // TO ABN (澳洲商業號碼)
	ToEmail               string               `json:"toEmail" gorm:"type:varchar(255);not null"`                              // TO 郵件
	ToAddress             string               `json:"toAddress" gorm:"type:varchar(255);not null"`                            // TO 地址
	Particular            string               `json:"particular" gorm:"type:text;not null"`                                   // 明細
	RejectReason          string               `json:"rejectReason" gorm:"type:text"`                                          // 拒絕原因
	Remark                string               `json:"remark" gorm:"type:text;not null"`                                       // 備註
	OtherRemark           string               `json:"otherRemark" gorm:"type:text;not null"`                                  // 其他備註
	Currency              string               `json:"currency" gorm:"type:varchar(3);not null"`                               // 貨幣 (ISO 4217)
	TotalAmount           decimal.Decimal      `json:"totalAmount" gorm:"type:decimal(9,2);not null"`                          // 總金額
	TaxAmount             decimal.Decimal      `json:"taxAmount" gorm:"type:decimal(9,2);not null"`                            // 稅額
	TaxRate               decimal.Decimal      `json:"taxRate" gorm:"type:decimal(5,3);not null"`                              // GST稅率 比例 1% 記錄 0.01
	SuperRate             decimal.Decimal      `json:"superRate" gorm:"type:decimal(5,3);not null"`                            // 退休金供款金额 比例 1% 記錄 0.01
	SuperAmount           decimal.Decimal      `json:"superAmount" gorm:"type:decimal(9,2);not null"`                          // 退休金供款金额
	CommissionAmount      decimal.Decimal      `json:"commissionAmount" gorm:"type:decimal(9,2);not null"`                     // 佣金金額
	GrandTotal            decimal.Decimal      `json:"grandTotal" gorm:"type:decimal(9,2);not null"`                           // 總金額
	PaymentReceived       string               `json:"paymentReceived" gorm:"type:varchar(1);not null"`                        // 是否已收到付款 Y/N
	PaymentReceivedTime   *time.Time           `json:"paymentReceivedTime" gorm:"type:datetime(0)"`                            // 收到付款時間
	Paid                  string               `json:"paid" gorm:"type:varchar(1);index:paid_idx;not null"`                    // 是否已經付款 Y/N
	PaidDate              xtype.NullDate       `json:"paidDate" gorm:"type:date"`                                              // 付款日期
	PaidUserId            uint64               `json:"paidUserId" gorm:"not null"`                                             // 付款人
	Progress              string               `json:"progress" gorm:"type:varchar(32);not null"`                              // 進度
	CreateTime            time.Time            `json:"createTime" gorm:"type:datetime(0);not null"`                            // 創建時間
	CreateUserId          uint64               `json:"createUserId" gorm:"index:create_user_idx;not null"`                     // 創建人
	UpdateTime            *time.Time           `json:"updateTime" gorm:"type:datetime(0)"`                                     // 更新時間
	UpdateUserId          uint64               `json:"updateUserId" gorm:"index:update_user_idx;not null"`                     // 更新人
	SentTime              *time.Time           `json:"sentTime" gorm:"type:datetime(0)"`                                       // 發送時間
	SentUserId            uint64               `json:"sentUserId" gorm:"index:sent_user_idx;not null"`                         // 發送人
	xmodel.Model
}

func (Document) TableName() string {
	return "document"
}

func (Document) SwaggerDescription() string {
	return "單據"
}
