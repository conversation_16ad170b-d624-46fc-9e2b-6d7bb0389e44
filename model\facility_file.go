package model

import (
	"github.com/Norray/xrocket/xmodel"
)

const (
	FacilityFileCodeSignedAgreement          = "SIGNED_AGREEMENT"           // 已簽署的協議
	FacilityFileCodePublicLiabilityInsurance = "PUBLIC_LIABILITY_INSURANCE" // 公共責任保險

	// 入職培訓文件 - 按職業類型區分
	FacilityFileCodeOrientationDocumentationMedicalPractitioner = "ORIENTATION_DOCUMENTATION_MEDICAL_PRACTITIONER" // 醫療從業員入職培訓文件
	FacilityFileCodeOrientationDocumentationEnrolledNurse       = "ORIENTATION_DOCUMENTATION_ENROLLED_NURSE"       // 登記護士入職培訓文件
	FacilityFileCodeOrientationDocumentationRegisteredNurse     = "ORIENTATION_DOCUMENTATION_REGISTERED_NURSE"     // 註冊護士入職培訓文件
	FacilityFileCodeOrientationDocumentationPersonalCareWorker  = "ORIENTATION_DOCUMENTATION_PERSONAL_CARE_WORKER" // 個人護理員入職培訓文件

	FacilityFileAiSuccessYes = "Y"
	FacilityFileAiSuccessNo  = "N"
)

var FacilityFileCodes = []string{
	FacilityFileCodeSignedAgreement,
	FacilityFileCodePublicLiabilityInsurance,
	FacilityFileCodeOrientationDocumentationMedicalPractitioner,
	FacilityFileCodeOrientationDocumentationEnrolledNurse,
	FacilityFileCodeOrientationDocumentationRegisteredNurse,
	FacilityFileCodeOrientationDocumentationPersonalCareWorker,
}

// 根據職業類型獲取對應的入職培訓文件代碼
var OrientationDocumentationCodeMap = map[string]string{
	"MEDICAL_PRACTITIONER": FacilityFileCodeOrientationDocumentationMedicalPractitioner,
	"ENROLLED_NURSE":       FacilityFileCodeOrientationDocumentationEnrolledNurse,
	"REGISTERED_NURSE":     FacilityFileCodeOrientationDocumentationRegisteredNurse,
	"PERSONAL_CARE_WORKER": FacilityFileCodeOrientationDocumentationPersonalCareWorker,
}

// 獲取職業類型對應的入職培訓文件代碼
func GetOrientationDocumentationCodeByProfession(profession string) string {
	if code, exists := OrientationDocumentationCodeMap[profession]; exists {
		return code
	}
	return "" // 不支持的職業類型返回空字符串
}

// 機構文件
type FacilityFile struct {
	Id                 uint64 `json:"id" gorm:"primary_key"`
	FacilityId         uint64 `json:"facilityId" gorm:"index:facilityId_idx;not null"`               // 機構ID
	FileCode           string `json:"fileCode" gorm:"type:varchar(255);index:fileCode_idx;not null"` // 文件代碼
	Mode               string `json:"mode" gorm:"type:varchar(255);not null"`                        // 在OSS中的私有還是公開
	Bucket             string `json:"bucket" gorm:"type:varchar(255);not null"`                      // Bucket
	Path               string `json:"path" gorm:"type:varchar(255);not null"`                        // Bucket下的路徑
	Uuid               string `json:"uuid" gorm:"type:varchar(255);index:uuid_idx;not null"`         // 唯一文件名
	OriginFileName     string `json:"originFileName" gorm:"type:varchar(255);not null"`              // 原文件名
	FileName           string `json:"fileName" gorm:"type:varchar(255);not null"`                    // 唯一文件名
	FileType           string `json:"fileType" gorm:"type:varchar(255);not null"`                    // 文件類型
	FileSize           uint32 `json:"fileSize" gorm:"not null"`
	ThumbnailPath      string `json:"thumbnailPath" gorm:"type:varchar(255);not null"` // 縮略圖路徑
	ThumbnailFileSize  uint32 `json:"thumbnailFileSize" gorm:"not null"`               // 縮略圖文件大小
	AiResultJson       string `json:"aiResultJson" gorm:"type:text;not null"`          // AI 提取信息結果（只有部分文件需要進行提取）
	AiModel            string `json:"aiModel" gorm:"type:varchar(255);not null"`       // AI 大模型名稱
	AiInputTokenUsage  int32  `json:"inputUsageToken" gorm:"not null"`                 // AI 使用了多少個 Input token
	AiOutputTokenUsage int32  `json:"outputUsageToken" gorm:"not null"`                // AI 使用了多少個 Output token
	xmodel.Model
}

type FacilityFileAiResultCache struct {
	ExpiryDate  string `json:"expiryDate"`  // 到期日(YYYY-MM-DD)
	Number      string `json:"number"`      // 號碼
	Description string `json:"description"` // 描述
}

func (FacilityFile) TableName() string {
	return "facility_file"
}

func (FacilityFile) SwaggerDescription() string {
	return "機構文件"
}

// 文件大小限制 (bytes)
func (FacilityFile) FileSizeLimitations() map[string]int64 {
	return map[string]int64{
		FacilityFileCodeSignedAgreement:                             FileSizeLimitImageFile,
		FacilityFileCodePublicLiabilityInsurance:                    FileSizeLimitDocumentFile,
		FacilityFileCodeOrientationDocumentationMedicalPractitioner: FileSizeLimitDocumentFile,
		FacilityFileCodeOrientationDocumentationEnrolledNurse:       FileSizeLimitDocumentFile,
		FacilityFileCodeOrientationDocumentationRegisteredNurse:     FileSizeLimitDocumentFile,
		FacilityFileCodeOrientationDocumentationPersonalCareWorker:  FileSizeLimitDocumentFile,
	}
}

// 允許的文件類型
func (FacilityFile) AllowedFileTypes() map[string][]string {
	return map[string][]string{
		FacilityFileCodeSignedAgreement:                             ImageFileTypes,
		FacilityFileCodePublicLiabilityInsurance:                    DocumentFileTypes,
		FacilityFileCodeOrientationDocumentationMedicalPractitioner: DocumentFileTypes,
		FacilityFileCodeOrientationDocumentationEnrolledNurse:       DocumentFileTypes,
		FacilityFileCodeOrientationDocumentationRegisteredNurse:     DocumentFileTypes,
		FacilityFileCodeOrientationDocumentationPersonalCareWorker:  DocumentFileTypes,
	}
}
