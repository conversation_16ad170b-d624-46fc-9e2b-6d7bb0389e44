package model

import "github.com/Norray/xrocket/xmodel"

// 用户部门关系表
type FacilityUserDepartment struct {
	Id           uint64 `json:"id" gorm:"primary_key"`
	UserId       uint64 `json:"userId" gorm:"not null;index:user_idx"`
	DepartmentId uint64 `json:"departmentId" gorm:"not null;index:department_idx"`
	xmodel.Model
}

func (FacilityUserDepartment) TableName() string {
	return "facility_user_department"
}

func (FacilityUserDepartment) SwaggerDescription() string {
	return "機構用戶部門管理"
}
