package model

import (
	"github.com/Norray/xrocket/xmodel"
)

const (
	FaqFileCodeImage = "FAQ_IMAGE" // 常見問題圖片
)

var FaqFileCodes = []string{
	FaqFileCodeImage,
}

// 常見問題文件
type FaqFile struct {
	Id             uint64 `json:"id" gorm:"primary_key"`
	FaqId          uint64 `json:"faqId" gorm:"index:faqId_idx;not null"`                         // 常見問題ID
	FileCode       string `json:"fileCode" gorm:"type:varchar(255);index:fileCode_idx;not null"` // 文件代碼
	Mode           string `json:"mode" gorm:"type:varchar(255);not null"`                        // 在OSS中的私有還是公開
	Bucket         string `json:"bucket" gorm:"type:varchar(255);not null"`                      // Bucket
	Path           string `json:"path" gorm:"type:varchar(255);not null"`                        // Bucket下的路徑
	Uuid           string `json:"uuid" gorm:"type:varchar(255);index:uuid_idx;not null"`         // 唯一文件名
	OriginFileName string `json:"originFileName" gorm:"type:varchar(255);not null"`              // 原文件名
	FileName       string `json:"fileName" gorm:"type:varchar(255);not null"`                    // 唯一文件名
	FileType       string `json:"fileType" gorm:"type:varchar(255);not null"`                    // 文件類型
	FileSize       uint32 `json:"fileSize" gorm:"not null"`
	xmodel.Model
}

func (FaqFile) TableName() string {
	return "faq_file"
}

func (FaqFile) SwaggerDescription() string {
	return "常見問題文件"
}

// 文件大小限制 (bytes)
func (FaqFile) FileSizeLimitations() map[string]int64 {
	return map[string]int64{
		FaqFileCodeImage: FileSizeLimitDocumentFile, // 5MB
	}
}

// 允許的文件類型
func (FaqFile) AllowedFileTypes() map[string][]string {
	return map[string][]string{
		FaqFileCodeImage: ImageFileTypes,
	}
}
