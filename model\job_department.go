package model

import (
	"github.com/Norray/xrocket/xmodel"
)

// JobDepartment 職位與部門關係
type JobDepartment struct {
	Id           uint64 `json:"id" gorm:"primary_key"` // Id
	DepartmentId uint64 `json:"departmentId" gorm:"index:department_idx;not null"`
	JobId        uint64 `json:"jobId" gorm:"index:job_idx;not null"` // 工作Id
	xmodel.Model
}

func (JobDepartment) TableName() string {
	return "job_department"
}

func (JobDepartment) SwaggerDescription() string {
	return "職位與部門關係"
}
