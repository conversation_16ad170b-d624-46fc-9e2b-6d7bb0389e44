package model

import (
	"fmt"

	"github.com/Norray/xrocket/xmodel"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

const (
	ServiceLocationStatusEnable  = "ENABLE"  // 啟用
	ServiceLocationStatusDisable = "DISABLE" // 停用
)

// 機構的服務地點管理
type ServiceLocation struct {
	Id             uint64          `json:"id" gorm:"primary_key"`
	FacilityId     uint64          `json:"facilityId" gorm:"index:facility_idx;not null"`                                     // 機構ID // 機構聯絡電郵
	Name           string          `json:"name" gorm:"type:varchar(255);not null;index:name_idx"`                             // 服務地點名稱
	Address        string          `json:"address" gorm:"type:varchar(255);not null"`                                         // 地址
	AddressExtra   string          `json:"addressExtra" gorm:"type:varchar(255);not null"`                                    // 地址附加信息
	LocationState  string          `json:"locationState" gorm:"type:varchar(128);index:location_state_idx;not null"`          // 州
	LocationCity   string          `json:"locationCity" gorm:"type:varchar(128);index:location_city_idx;not null"`            // 城市
	LocationRoute  string          `json:"locationRoute" gorm:"type:varchar(128);index:location_route_idx;not null"`          // 街道
	LocationLat    decimal.Decimal `json:"locationLat" gorm:"type:decimal(12,8);not null"`                                    // 緯度
	LocationLng    decimal.Decimal `json:"locationLng" gorm:"type:decimal(12,8);not null"`                                    // 經度
	LocationPoint  []byte          `json:"locationPoint" gorm:"type:point;srid:4326;spatialIndex:location_point_spatial_idx"` // 地理坐標點
	Timezone       string          `json:"timezone" gorm:"type:varchar(64);not null"`                                         // 時區
	TimezoneOffset string          `json:"timezoneOffset" gorm:"type:varchar(6);not null"`                                    // 時區偏移量（+08:00）
	Status         string          `json:"status" gorm:"type:varchar(32);index:status_idx;not null"`                          // 狀態
	xmodel.Model
}

func (sl ServiceLocation) BeforeSave(tx *gorm.DB) (err error) {
	// 在保存記錄前忽略 location_point 字段
	tx.Omit("location_point")
	return nil
}

func (sl ServiceLocation) AfterSave(tx *gorm.DB) (err error) {
	// 保存後更新 location_point 字段
	pointSQL := "UPDATE service_location SET location_point = ST_PointFromText(?, 4326) WHERE id = ?"
	pointWKT := fmt.Sprintf("POINT(%s %s)", sl.LocationLng.String(), sl.LocationLat.String())
	if err := tx.Exec(pointSQL, pointWKT, sl.Id).Error; err != nil {
		return err
	}
	return nil
}

func (ServiceLocation) TableName() string {
	return "service_location"
}

func (ServiceLocation) SwaggerDescription() string {
	return "機構的服務地點管理"
}
