package model

import (
	"time"

	"github.com/Norray/xrocket/xmodel"
)

// 通知類型常量
const (
	// Professional - 工作相關通知
	SystemNotificationTypeProfessionalJobInvitation          = "PROFESSIONAL_JOB_INVITATION"           // 工作邀請
	SystemNotificationTypeProfessionalJobAccepted            = "PROFESSIONAL_JOB_ACCEPTED"             // 工作接受
	SystemNotificationTypeProfessionalJobCancelled           = "PROFESSIONAL_JOB_CANCELLED"            // 工作取消
	SystemNotificationTypeProfessionalJobCompensation        = "PROFESSIONAL_JOB_COMPENSATION"         // 工作賠付
	SystemNotificationTypeProfessionalJobNewAvailable        = "PROFESSIONAL_JOB_NEW_AVAILABLE"        // 新工作可用
	SystemNotificationTypeProfessionalJobOrientationDocument = "PROFESSIONAL_JOB_ORIENTATION_DOCUMENT" // 閱讀工作指導文件

	// Professional - 日程相關通知
	SystemNotificationTypeProfessionalCalendar24Hour = "PROFESSIONAL_CALENDAR_24_HOUR" // 24小時提醒
	SystemNotificationTypeProfessionalCalendar2Hour  = "PROFESSIONAL_CALENDAR_2_HOUR"  // 2小時提醒

	// Professional - 賬單相關通知
	SystemNotificationTypeProfessionalBillingConfirmationNoteGenerate = "PROFESSIONAL_BILLING_CONFIRMATION_NOTE_GENERATE" // 生成確認單
	SystemNotificationTypeProfessionalBillingConfirmationNoteApproved = "PROFESSIONAL_BILLING_CONFIRMATION_NOTE_APPROVED" // 確認單通過
	SystemNotificationTypeProfessionalBillingConfirmationNoteRejected = "PROFESSIONAL_BILLING_CONFIRMATION_NOTE_REJECTED" // 確認單拒絕

	// Professional - 個人資料相關通知
	SystemNotificationTypeProfessionalProfileApproved           = "PROFESSIONAL_PROFILE_APPROVED"             // 資料通過
	SystemNotificationTypeProfessionalProfileRejected           = "PROFESSIONAL_PROFILE_REJECTED"             // 資料駁回
	SystemNotificationTypeProfessionalProfileNeedSuper          = "PROFESSIONAL_PROFILE_NEED_SUPER"           // 需要完善super資料
	SystemNotificationTypeProfessionalProfileNeedPaymentDetails = "PROFESSIONAL_PROFILE_NEED_PAYMENT_DETAILS" // 需要完善Payment Details資料
	SystemNotificationTypeProfessionalProfileDocumentExpire     = "PROFESSIONAL_PROFILE_DOCUMENT_EXPIRE"      // 文件即將到期
	SystemNotificationTypeProfessionalProfileReferenceDeclined  = "PROFESSIONAL_PROFILE_REFERENCE_DECLINED"   // 推薦人拒絕

	// Facility - 工作相關通知
	SystemNotificationTypeFacilityJobAccepted              = "FACILITY_JOB_ACCEPTED"                // 工作接受
	SystemNotificationTypeFacilityJobCancelled             = "FACILITY_JOB_CANCELLED"               // 工作取消
	SystemNotificationTypeFacilityJobDecline               = "FACILITY_JOB_DECLINE"                 // 工作拒絕
	SystemNotificationTypeFacilityJobInvitationExpired     = "FACILITY_JOB_INVITATION_EXPIRED"      // 工作邀請過期
	SystemNotificationTypeFacilityJobNoApplication         = "FACILITY_JOB_NO_APPLICATION"          // 工作無申請
	SystemNotificationTypeFacilityJobClosedForApplications = "FACILITY_JOB_CLOSED_FOR_APPLICATIONS" // 工作結束招聘
	SystemNotificationTypeFacilityJobNeedConfirm           = "FACILITY_JOB_NEED_CONFIRM"            // 工作需要確認

	// Facility - 日程相關通知
	SystemNotificationTypeFacilityCalendar24Hour = "FACILITY_CALENDAR_24_HOUR" // 24小時提醒

	// Facility - 賬單相關通知
	SystemNotificationTypeFacilityBillingConfirmationNoteReceived = "FACILITY_BILLING_CONFIRMATION_NOTE_RECEIVED" // 收到確認單
	SystemNotificationTypeFacilityBillingInvoiceReceived          = "FACILITY_BILLING_INVOICE_RECEIVED"           // 收到發票

	// Facility - 機構信息相關通知
	SystemNotificationTypeFacilityInformationApproved = "FACILITY_INFORMATION_APPROVED" // 機構信息通過
	SystemNotificationTypeFacilityInformationRejected = "FACILITY_INFORMATION_REJECTED" // 機構信息駁回

	// 系統公告類型 (適用於所有用戶)
	SystemNotificationTypeSystemAnnouncement = "SYSTEM_ANNOUNCEMENT" // 系統公告
	SystemNotificationTypeMaintenanceNotice  = "MAINTENANCE_NOTICE"  // 維護通知
)

// 關聯業務類型常量 (RelatedType)
const (
	// 工作相關
	SystemNotificationRelatedTypeJob            = "JOB"             // 工作
	SystemNotificationRelatedTypeJobApplication = "JOB_APPLICATION" // 工作申請
	SystemNotificationRelatedTypeJobShift       = "JOB_SHIFT"       // 工作班次

	// 賬單相關
	SystemNotificationRelatedTypeConfirmationNote = "CONFIRMATION_NOTE" // 確認單
	SystemNotificationRelatedTypeInvoice          = "INVOICE"           // 發票

	// 個人資料相關
	SystemNotificationRelatedTypeProfessionalProfile              = "PROFESSIONAL_PROFILE"        // 個人資料
	SystemNotificationRelatedTypeProfessionalFile                 = "PROFESSIONAL_FILE"           // 文件
	SystemNotificationRelatedTypeProfessionalReference            = "PROFESSIONAL_REFERENCE"      // 推薦人
	SystemNotificationRelatedTypeProfessionalSuperannuation       = "PROFESSIONAL_SUPERANNUATION" // 養老金
	SystemNotificationRelatedTypeProfessionalProfessionalTraining = "PROFESSIONAL_TRAINING"       // 專業培訓

	// 機構信息相關
	SystemNotificationRelatedTypeFacility        = "FACILITY"         // 機構
	SystemNotificationRelatedTypeFacilityFile    = "FACILITY_FILE"    // 機構文件
	SystemNotificationRelatedTypeFacilityProfile = "FACILITY_PROFILE" // 機構資料
)

// 通知目標類型常量
const (
	SystemNotificationTargetTypeProfessional = "PROFESSIONAL" // 專業人士
	SystemNotificationTargetTypeFacility     = "FACILITY"     // 機構
	SystemNotificationTargetTypeAll          = "ALL"          // 所有用戶
	SystemNotificationTargetTypeSystemAdmin  = "SYSTEM_ADMIN" // 系統管理員
)

// 通知優先級常量
const (
	SystemNotificationPriorityLow    = "LOW"    // 低優先級
	SystemNotificationPriorityNormal = "NORMAL" // 普通優先級
	SystemNotificationPriorityHigh   = "HIGH"   // 高優先級
	SystemNotificationPriorityUrgent = "URGENT" // 緊急
)

// 系統通知
type SystemNotification struct {
	Id               uint64    `json:"id" gorm:"primary_key"`
	NotificationType string    `json:"notificationType" gorm:"type:varchar(64);index:notification_type_idx;not null"` // 通知類型
	TargetType       string    `json:"targetType" gorm:"type:varchar(32);index:target_type_idx;not null"`             // 目標類型: PROFESSIONAL, FACILITY, ALL, SYSTEM_ADMIN
	Priority         string    `json:"priority" gorm:"type:varchar(16);default:'NORMAL';not null"`                    // 優先級: LOW, NORMAL, HIGH, URGENT
	Title            string    `json:"title" gorm:"type:varchar(512);not null"`                                       // 通知標題
	Content          string    `json:"content" gorm:"type:text;not null"`                                             // 通知內容
	ActionUrl        string    `json:"actionUrl" gorm:"type:varchar(512)"`                                            // 操作連結
	ActionText       string    `json:"actionText" gorm:"type:varchar(128)"`                                           // 操作按鈕文字
	RelatedId        uint64    `json:"relatedId" gorm:"index:related_id_idx"`                                         // 關聯業務ID（如工作ID、申請ID等）
	RelatedType      string    `json:"relatedType" gorm:"type:varchar(64);index:related_type_idx"`                    // 關聯業務類型（如 JOB, APPLICATION, PROFILE等）
	Metadata         string    `json:"metadata" gorm:"type:text"`                                                     // 額外元數據JSON
	CreatorUserId    uint64    `json:"creatorUserId" gorm:"index:creator_user_idx"`                                   // 創建用戶ID (0表示系統生成)
	CreateTime       time.Time `json:"createTime" gorm:"type:datetime(0);not null"`                                   // 創建時間
	xmodel.Model
}

func (m SystemNotification) TableName() string {
	return "system_notification"
}

func (m SystemNotification) SwaggerDescription() string {
	return "系統通知"
}
