<!DOCTYPE html>
<html>
<head>
    <title>Google reCAPTCHA 測試</title>
    <script src="https://www.google.com/recaptcha/api.js?render=6LfkFfkqAAAAAP6ljWc0G457MVY23G4LcKrz3m6S"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            word-break: break-all;
        }
        button {
            padding: 10px 20px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>Google reCAPTCHA 測試</h1>
    <p>點擊按鈕生成 token：</p>
    <button onclick="generateToken()">生成 Token</button>
    <div class="result" id="tokenResult"></div>
</div>

<script>
    function generateToken() {
        grecaptcha.execute('6LfkFfkqAAAAAP6ljWc0G457MVY23G4LcKrz3m6S', {action: 'register'})
            .then(function(token) {
                document.getElementById('tokenResult').innerHTML = token;
            });
    }
</script>
</body>
</html>