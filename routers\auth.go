package routers

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/gin-gonic/gin"
)

func authPublicRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1").Use(handlers...)
	{
		con := v1.NewAuthController()
		r.POST("/login", con.Login) // 登入
		r.POST("/google-oauth", con.GoogleOAuth)
		r.POST("/google-callback", con.GoogleCallback)
		r.POST("/auth/actions/forget-user-password", con.ForgetUserPassword)     // 忘記密碼
		r.POST("/auth/actions/reset-password", con.ResetPassword)                // 重置密碼
		r.POST("/auth/actions/reset-password-check", con.ResetPasswordUuidCheck) // 檢查重置密碼uuid是否有效
	}
}
