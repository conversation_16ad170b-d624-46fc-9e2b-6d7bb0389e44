package routers

import (
	"encoding/json"
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/shopspring/decimal"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

var testDocumentId uint64

func TestConfirmationNoteList(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/confirmation-notes",
		UserId:           9,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ProfessionalConfirmationNoteListReq{
					//Progress:  "DRAFT",
					//ToName:    "Centre",
					Particular: "",
					StartDate:  "",
					EndDate:    "",
				},
				SortingSet: xresp.SortingSet{
					SortingKey:  "createTime",
					SortingType: "d",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestConfirmationNoteInquire(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/confirmation-notes/actions/inquire",
		UserId:           45,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ConfirmationNoteInquireReq{
					DocumentId: 29,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestConfirmationNoteCreate(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/confirmation-notes/actions/create",
		UserId:           9,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "新增",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ConfirmationNoteCreateReq{
					JobApplicationId:      7,
					FromName:              "felix",
					FromBankStateBranch:   "********",
					FromBankAccountNumber: "***********",
					FromBankAccountName:   "test",
					ToName:                "Sydney Primecare Medical Centre",
					ToAddress:             "Alice Springs Desert Park, Flynn NT, Australia",
					ToAbn:                 "***********",
					WagesItem: services.ConfirmationNoteItemSummaryReq{
						Items: []services.ConfirmationNoteItemReq{
							{
								JobShiftId:    671,
								ItemDate:      "2025-06-08",
								ItemType:      model.DocumentItemTypeWages,
								StartTime:     "09:00",
								NextDay:       "N",
								FinishTime:    "18:00",
								BreakDuration: decimal.NewFromInt(1),
								Particular:    "正常工作",
								Hours:         decimal.NewFromInt(8),
								HourlyRate:    decimal.NewFromInt(100),
								TotalAmount:   decimal.NewFromInt(800),
								TaxAmount:     decimal.NewFromFloat(80),
								CountTax:      "Y",
							},
						},
						TotalAmount: decimal.NewFromInt(800),
						SuperAmount: decimal.NewFromInt(96),
						TaxAmount:   decimal.NewFromFloat(80),
						GrandTotal:  decimal.NewFromFloat(880),
					},
					OtherItem: services.ConfirmationNoteItemSummaryReq{
						Items: []services.ConfirmationNoteItemReq{
							{
								ItemDate:    "2025-06-08",
								ItemType:    model.DocumentItemTypeMeals,
								StartTime:   "",
								NextDay:     "N",
								FinishTime:  "",
								Particular:  "午餐費用",
								TotalAmount: decimal.NewFromInt(25),
								CountTax:    "N",
							},
						},
						TotalAmount: decimal.NewFromInt(25),
						GrandTotal:  decimal.NewFromFloat(25),
					},
					WagesDocumentFileIds: nil,
					OtherDocumentFileIds: nil,
					TotalAmount:          decimal.NewFromInt(825),
					SuperAmount:          decimal.NewFromInt(96),
					TaxAmount:            decimal.NewFromFloat(80),
					GrandTotal:           decimal.NewFromFloat(905),
				},
				CheckResultHandler: func(resp interface{}) bool {
					response := resp.(xresp.Response)
					var data services.ConfirmationNoteCreateResp
					dataByte, _ := json.Marshal(response.Data)
					_ = json.Unmarshal(dataByte, &data)
					testDocumentId = data.DocumentId
					return true
				},
			},
			{
				SubName:           "正常-無 tax(因為 JobShift 無法保存)",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ConfirmationNoteCreateReq{
					JobApplicationId:      7,
					FromName:              "felix1 li",
					FromBankStateBranch:   "123",
					FromBankAccountNumber: "123",
					FromBankAccountName:   "test",
					ToName:                "silence",
					ToAddress:             "456",
					ToAbn:                 "123",
					WagesItem: services.ConfirmationNoteItemSummaryReq{
						Items: []services.ConfirmationNoteItemReq{
							{
								JobShiftId:    671,
								ItemDate:      "2025-06-08",
								ItemType:      model.DocumentItemTypeWages,
								StartTime:     "09:00",
								NextDay:       "N",
								FinishTime:    "18:00",
								BreakDuration: decimal.NewFromInt(1),
								Particular:    "正常工作",
								Hours:         decimal.NewFromInt(8),
								HourlyRate:    decimal.NewFromInt(10),
								TotalAmount:   decimal.NewFromInt(80),
								TaxAmount:     decimal.NewFromFloat(0),
								CountTax:      "N",
							},
						},
						TotalAmount: decimal.NewFromInt(80),
						SuperAmount: decimal.NewFromFloat(9.6),
						TaxAmount:   decimal.NewFromFloat(0),
						GrandTotal:  decimal.NewFromFloat(89.6),
					},
					OtherItem: services.ConfirmationNoteItemSummaryReq{
						Items:       []services.ConfirmationNoteItemReq{},
						TotalAmount: decimal.NewFromInt(0),
						GrandTotal:  decimal.NewFromFloat(0),
					},
					WagesDocumentFileIds: nil,
					OtherDocumentFileIds: nil,
					TotalAmount:          decimal.NewFromInt(80),
					SuperAmount:          decimal.NewFromFloat(9.6),
					TaxAmount:            decimal.NewFromFloat(0),
					GrandTotal:           decimal.NewFromFloat(89.6),
				},
				CheckResultHandler: func(resp interface{}) bool {
					response := resp.(xresp.Response)
					var data services.ConfirmationNoteCreateResp
					dataByte, _ := json.Marshal(response.Data)
					_ = json.Unmarshal(dataByte, &data)
					testDocumentId = data.DocumentId
					return true
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestConfirmationNoteUpdate(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/confirmation-notes/actions/update",
		UserId:           9,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "修改",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ConfirmationNoteUpdateReq{
					DocumentId: 113,
					WagesItem: services.ConfirmationNoteItemSummaryReq{
						Items: []services.ConfirmationNoteItemReq{
							{
								JobShiftId:    671,
								ItemDate:      "2025-06-08",
								ItemType:      model.DocumentItemTypeWages,
								StartTime:     "09:00",
								NextDay:       "N",
								FinishTime:    "17:00",
								Particular:    "正常工作",
								Hours:         decimal.NewFromInt(8),
								ExpectedHours: decimal.NewFromInt(8),
								HourlyRate:    decimal.NewFromInt(100),
								TotalAmount:   decimal.NewFromInt(800),
								TaxAmount:     decimal.NewFromFloat(80),
								CountTax:      "Y",
							},
						},
						TotalAmount: decimal.NewFromInt(800),
						SuperAmount: decimal.NewFromFloat(96),
						TaxAmount:   decimal.NewFromFloat(80),
						GrandTotal:  decimal.NewFromFloat(880),
					},
					OtherItem: services.ConfirmationNoteItemSummaryReq{
						Items: []services.ConfirmationNoteItemReq{
							{
								ItemDate:    "2025-06-08",
								ItemType:    model.DocumentItemTypeMeals,
								StartTime:   "",
								NextDay:     "N",
								FinishTime:  "",
								Particular:  "午餐費用",
								TotalAmount: decimal.NewFromInt(25),
								CountTax:    "N",
							},
						},
						TotalAmount: decimal.NewFromInt(25),
						GrandTotal:  decimal.NewFromFloat(25),
					},
					WagesDocumentFileIds: []uint64{1},
					OtherDocumentFileIds: nil,
					TotalAmount:          decimal.NewFromInt(825),
					SuperAmount:          decimal.NewFromFloat(96),
					TaxAmount:            decimal.NewFromFloat(80),
					GrandTotal:           decimal.NewFromFloat(905),
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestConfirmationNoteSubmit(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/confirmation-notes/actions/submit",
		UserId:           9,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "提交",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ConfirmationNoteSubmitReq{
					DocumentId:            6,
					JobApplicationId:      0,
					FromName:              "",
					FromBankStateBranch:   "",
					FromBankAccountNumber: "",
					ToName:                "",
					ToAddress:             "",
					ToAbn:                 "",
					WagesItem: services.ConfirmationNoteItemSummaryReq{
						Items: []services.ConfirmationNoteItemReq{
							{
								ItemDate:      "2025-06-08",
								ItemType:      model.DocumentItemTypeWages,
								StartTime:     "09:00",
								NextDay:       "N",
								FinishTime:    "17:00",
								Particular:    "正常工作",
								Hours:         decimal.NewFromInt(8),
								ExpectedHours: decimal.NewFromInt(8),
								HourlyRate:    decimal.NewFromInt(101),
								TotalAmount:   decimal.NewFromInt(808),
								TaxAmount:     decimal.NewFromFloat(80.8),
								CountTax:      "Y",
							},
						},
						TotalAmount: decimal.NewFromInt(808),
						SuperAmount: decimal.NewFromFloat(96.96),
						TaxAmount:   decimal.NewFromFloat(80.8),
						GrandTotal:  decimal.NewFromFloat(985.76),
					},
					OtherItem: services.ConfirmationNoteItemSummaryReq{
						Items: []services.ConfirmationNoteItemReq{
							{
								ItemDate:    "2025-06-08",
								ItemType:    model.DocumentItemTypeMeals,
								StartTime:   "",
								NextDay:     "N",
								FinishTime:  "",
								Particular:  "午餐費用",
								TotalAmount: decimal.NewFromInt(25),
								CountTax:    "N",
							},
						},
						TotalAmount: decimal.NewFromInt(25),
						GrandTotal:  decimal.NewFromFloat(25),
					},
					WagesDocumentFileIds: []uint64{1},
					OtherDocumentFileIds: nil,
					TotalAmount:          decimal.NewFromInt(833),
					SuperAmount:          decimal.NewFromFloat(96.96),
					TaxAmount:            decimal.NewFromFloat(80.8),
					GrandTotal:           decimal.NewFromFloat(1010.76),
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestConfirmationNoteCancel(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/confirmation-notes/actions/cancel",
		UserId:           9,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "取消",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ConfirmationNoteCancelReq{
					DocumentId: 84,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityConfirmationNoteReview(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/confirmation-notes/actions/review",
		UserId:           17,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "審批",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityConfirmationNoteReviewReq{
					DocumentId: 1392,
					Action:     model.DocumentProgressConfirm,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityConfirmationNoteList(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/confirmation-notes",
		UserId:           16,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityConfirmationNoteListReq{
					JobId: 197,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityConfirmationNoteInquire(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/confirmation-notes/actions/inquire",
		UserId:           16,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ConfirmationNoteInquireReq{
					DocumentId: 113,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalConfirmationNoteJobList(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/confirmation-notes/actions/job-list",
		UserId:           15,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "可以申請確認通知單的工作列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.ConfirmationNoteJobListReq{},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalConfirmationNoteJobDocumentShiftTimeList(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/confirmation-notes/actions/job-shift-time-list",
		UserId:           15,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "申請確認通知單工作的班次",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobShiftTimeListReq{
					DocumentId: 59,
					FacilityId: 7,
					JobId:      262,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
