package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/gin-gonic/gin"
)

func departmentFacilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		r.POST("/departments/actions/create", facility_api.NewDepartmentController().Create)
		r.GET("/departments", facility_api.NewDepartmentController().List)
		r.GET("/departments/actions/search", facility_api.NewDepartmentController().Search)
		r.POST("/departments/actions/edit", facility_api.NewDepartmentController().Edit)
		r.GET("/departments/actions/inquire", facility_api.NewDepartmentController().Inquire)
		r.POST("/departments/actions/delete", facility_api.NewDepartmentController().Delete)
	}
}
