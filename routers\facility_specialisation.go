package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/gin-gonic/gin"
)

func facilitySpecialisationFacilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		controller := facility_api.NewFacilitySpecialisationController()
		r.GET("/facility-specialisations/actions/inquire", controller.Inquire)                       // 查詢機構專業細分樹狀結構
		r.GET("/facility-specialisations/actions/inquire-by-position", controller.InquireByPosition) // 獲取指定職位的機構專業細分
		r.POST("/facility-specialisations/actions/edit", controller.Edit)                            // 更新機構專業細分
	}
}
