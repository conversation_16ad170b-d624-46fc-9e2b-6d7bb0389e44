package routers

import (
	"github.com/Norray/medic-crew/api/v1/facility_api"
	"github.com/Norray/medic-crew/api/v1/professional_api"
	"github.com/Norray/medic-crew/api/v1/system_api"
	"github.com/gin-gonic/gin"
)

func jobFacilityRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/facility").Use(handlers...)
	{
		controller := facility_api.NewJobController()
		r.GET("/jobs", controller.List)                                                      // 查詢工作職位列表
		r.GET("/jobs/actions/search", controller.Search)                                     // 搜索工作職位
		r.GET("/jobs/actions/inquire", controller.Inquire)                                   // 查詢工作職位詳情
		r.POST("/jobs/actions/create", controller.Create)                                    // 創建工作職位
		r.POST("/jobs/actions/edit", controller.Edit)                                        // 編輯工作職位
		r.POST("/jobs/actions/delete", controller.Delete)                                    // 刪除工作職位
		r.POST("/jobs/actions/update-status", controller.UpdateStatus)                       // 更新工作職位狀態
		r.POST("/jobs/actions/update-calendar-note", controller.UpdateCalendarNote)          // 更新工作日曆備註
		r.POST("/jobs/actions/withdraw-invite", controller.WithdrawInvite)                   // 撤回邀請
		r.POST("/jobs/actions/update-shift-allocation", controller.JobUpdateShiftAllocation) // 更新班次分配方式
	}
}

func jobProfessionalRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/professional").Use(handlers...)
	{
		controller := professional_api.NewJobController()
		r.GET("/jobs/actions/search", controller.Search)   // 搜索工作職位
		r.GET("/jobs/actions/inquire", controller.Inquire) // 查詢工作職位詳情
		r.POST("/jobs/actions/apply", controller.Apply)    // 申請工作職位
	}
}

func jobSystemRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/system").Use(handlers...)
	{
		controller := system_api.NewJobController()
		r.GET("/jobs", controller.List)                    // 查詢工作職位列表
		r.GET("/jobs/actions/search", controller.Search)   // 搜索工作職位
		r.GET("/jobs/actions/inquire", controller.Inquire) // 查詢工作職位詳情
	}
}
