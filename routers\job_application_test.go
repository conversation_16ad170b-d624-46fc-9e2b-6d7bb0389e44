package routers

import (
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

// region ------------------------------------------ 機構端 ------------------------------------------

// TestJobApplicationList 測試工作申請列表查詢
func TestJobApplicationList(t *testing.T) {
	facilityId := uint64(3)
	jobId := uint64(9)
	test := xtest.Test{
		Url:              programPath + "/v1/facility/job-applications",
		UserId:           11,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "列表查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobApplicationListForFacilityReq{
					FacilityId: facilityId,
					JobId:      jobId,
				},
			},
			{
				SubName:           "按狀態查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobApplicationListForFacilityReq{
					FacilityId: facilityId,
					JobId:      jobId,
					Status:     model.JobApplicationStatusApply,
				},
			},
			{
				SubName:           "工作不存在",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobApplicationListForFacilityReq{
					FacilityId: facilityId,
					JobId:      99999, // 假設不存在的Id
				},
			},
			{
				SubName:           "缺少FacilityId",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobApplicationListForFacilityReq{
					JobId: 1,
				},
			},
			{
				SubName:           "缺少JobId",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobApplicationListForFacilityReq{
					FacilityId: facilityId,
				},
			},
			{
				SubName:           "狀態無效",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobApplicationListForFacilityReq{
					FacilityId: facilityId,
					JobId:      jobId,
					Status:     "INVALID_STATUS",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// TestJobApplicationInviteList 測試工作職位邀請列表查詢
func TestJobApplicationInviteList(t *testing.T) {
	user := getTestUser(16)
	test := xtest.Test{
		Url:              programPath + "/v1/facility/job-applications/actions/invite-list",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "邀請列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobApplicationInviteListReq{
					FacilityId: user.FacilityId,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// TestJobApplicationInquire 測試工作申請詳情查詢
func TestJobApplicationInquire(t *testing.T) {
	facilityId := uint64(3)
	applicationId := uint64(2)
	test := xtest.Test{
		Url:              programPath + "/v1/facility/job-applications/actions/inquire",
		UserId:           11,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "詳情查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobApplicationInquireForFacilityReq{
					FacilityId:       facilityId,
					JobApplicationId: applicationId,
				},
			},
			{
				SubName:           "申請不存在",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobApplicationInquireForFacilityReq{
					FacilityId:       facilityId,
					JobApplicationId: 99999, // 假設不存在的Id
				},
			},
			{
				SubName:           "缺少FacilityId",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobApplicationInquireForFacilityReq{
					JobApplicationId: applicationId,
				},
			},
			{
				SubName:           "缺少ApplicationId",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobApplicationInquireForFacilityReq{
					FacilityId: facilityId,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityCalendarDay(t *testing.T) {
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/jobs/actions/calendar-day",
		UserId:           43,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "日曆",
		Cases: []xtest.TestCase{
			{
				SubName:           "日曆上用",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobCalendarDayReq{
					FacilityId:        14,
					CalendarBeginTime: "2025-06-14 00:00:00",
					CalendarEndTime:   "2025-06-14 23:59:59",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityCalendarMonth(t *testing.T) {
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/jobs/actions/calendar-month",
		UserId:           16,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "日曆",
		Cases: []xtest.TestCase{
			{
				SubName:           "日曆上用",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobCalendarMonthReq{
					FacilityId:        7,
					CalendarBeginTime: "2025-05-01 00:00:00",
					CalendarEndTime:   "2025-05-31 23:59:59",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityJobHomeSchedule(t *testing.T) {
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/jobs/actions/home-schedule",
		UserId:           43,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "首頁日曆排程",
		Cases: []xtest.TestCase{
			{
				SubName:           "首頁排程查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobHomeScheduleReq{
					FacilityId: 14,
					BeginTime:  "2025-06-14 00:00:00",
					EndTime:    "2025-06-14 23:59:59",
					Timezone:   "Asia/Hong_Kong",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityJobApplicationCancel(t *testing.T) {
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/job-applications/actions/cancel",
		UserId:           15,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "申請",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.FacilityJobApplicationCancelReq{
					JobId:             96,
					JobApplicationIds: []uint64{3},
					FacilityId:        7,
					CancelReason:      "測試",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestFacilityJobApplicationListByFacilitySession(t *testing.T) {
	user := getTestUser(16)
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/facility/job-applications/actions/list-by-facility-session",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "列表查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobApplicationListByFacilitySessionReq{
					FacilityId:         user.FacilityId,
					ProfessionalUserId: 15,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// endregion ------------------------------------------ 機構端 ------------------------------------------

// region ------------------------------------------ 專業人士端 ------------------------------------------

func TestProfessionalMyJob(t *testing.T) {
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/my-jobs",
		UserId:           48,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "搜索",
		Cases: []xtest.TestCase{
			{
				SubName:           "按距離排序",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.MyJobListReq{
					Status: model.JobApplicationStatusApply,
				},
				PageSet: xresp.PageSet{
					PageIndex: 1,
					PageSize:  30,
				},
			},
			{
				SubName:           "日曆上用",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.MyJobListReq{
					Status:            model.JobApplicationStatusAccept,
					CalendarBeginTime: "2025-05-01 00:00:00",
					CalendarEndTime:   "2025-05-31 23:59:59",
				},
				SortingSet: xresp.SortingSet{
					SortingKey:  "begin_time",
					SortingType: "a",
				},
				PageSet: xresp.PageSet{
					PageIndex: 1,
					PageSize:  30,
				},
			},
			{
				SubName:           "待處理邀請列表",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.MyJobListReq{
					Status: model.JobApplicationStatusInvite,
					// CalendarBeginTime: "2025-05-01 00:00:00",
					// CalendarEndTime:   "2025-05-31 23:59:59",
				},
				SortingSet: xresp.SortingSet{
					SortingKey:  "begin_time",
					SortingType: "a",
				},
				PageSet: xresp.PageSet{
					PageIndex: 1,
					PageSize:  30,
				},
			},
			{
				SubName:           "僅顯示有待生成確認單的工作",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.MyJobListReq{
					Progress:        "COMPLETE",
					UninvoicedShift: "Y",
					// Status: model.JobApplicationStatusInvite,
					// CalendarBeginTime: "2025-05-01 00:00:00",
					// CalendarEndTime:   "2025-05-31 23:59:59",
				},
				SortingSet: xresp.SortingSet{
					SortingKey:  "begin_time",
					SortingType: "a",
				},
				PageSet: xresp.PageSet{
					PageIndex: 1,
					PageSize:  30,
				},
			},
			{
				SubName:           "顯示24小時工作",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.MyJobListReq{
					Status:            "ACCEPT",
					NeedShiftTime:     "Y",
					CalendarBeginTime: "2025-09-19 15:20:00",
					CalendarEndTime:   "2025-09-20 15:20:59",
					Timezone:          "Asia/Shanghai",
				},
				SortingSet: xresp.SortingSet{
					SortingKey:  "begin_time",
					SortingType: "a",
				},
				PageSet: xresp.PageSet{
					PageIndex: 1,
					PageSize:  30,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalMyJobCancel(t *testing.T) {
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/my-jobs/actions/cancel",
		UserId:           15,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "申請",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.MyJobCancelReq{
					JobApplicationId: 3,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalMyJobWithdraw(t *testing.T) {
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/my-jobs/actions/withdraw",
		UserId:           15,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "申請",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.MyJobWithdrawReq{
					JobApplicationId: 3,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalMyJobUpdateCalendarNote(t *testing.T) {
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/my-jobs/actions/update-calendar-note",
		UserId:           15,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "更新備註",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常更新",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.MyJobUpdateCalendarNoteReq{
					JobApplicationId: 3,
					Remark:           "這是一個測試備註",
				},
			},
			{
				SubName:           "申請不存在",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.MyJobUpdateCalendarNoteReq{
					JobApplicationId: 99999,
					Remark:           "這是一個測試備註",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalJobAcceptInvite(t *testing.T) {
	user := getTestUser(15)
	test := xtest.Test{
		Url:              programPath + "/v1/professional/my-jobs/actions/accept-invite",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "接受邀請",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobAcceptInviteReq{
					FacilityId:       7,
					JobId:            197,
					JobApplicationId: 13,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalJobApplicationListByProfessionalSession(t *testing.T) {
	user := getTestUser(15)
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/job-applications/actions/list-by-professional-session",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "列表查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobApplicationListByProfessionalSessionReq{
					FacilityId: 7,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalJobApplicationChat(t *testing.T) {
	user := getTestUser(15)
	test := xtest.Test{
		Url:              programPath + "/v1/professional/job-applications/actions/chat",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "打開聊天會話",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ChatByProfessionalReq{
					JobApplicationId: 3,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalJobApplicationHomeSchedule(t *testing.T) {
	user := getTestUser(85)
	test := xtest.Test{
		Url:              programPath + "/v1/professional/job-applications/actions/home-schedule",
		UserId:           user.UserId,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "獲取專業人員首頁日程",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobHomeScheduleByProfessionalReq{
					BeginTime: "2025-09-19 16:12:57",
					EndTime:   "2025-09-20 16:12:57",
					Timezone:  "Australia/Sydney",
				},
			},
			{
				SubName:           "缺少開始時間",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobHomeScheduleByProfessionalReq{
					EndTime:  "2024-01-31 23:59:59",
					Timezone: "Australia/Sydney",
				},
			},
			{
				SubName:           "缺少結束時間",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobHomeScheduleByProfessionalReq{
					BeginTime: "2024-01-01 00:00:00",
					Timezone:  "Australia/Sydney",
				},
			},
			{
				SubName:           "缺少時區",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobHomeScheduleByProfessionalReq{
					BeginTime: "2024-01-01 00:00:00",
					EndTime:   "2024-01-31 23:59:59",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// endregion ------------------------------------------ 專業人士端 ------------------------------------------

// region ------------------------------------------ 管理端 ------------------------------------------
func TestJobApplicationSystemList(t *testing.T) {
	facilityId := uint64(7)
	jobId := uint64(9)
	test := xtest.Test{
		Url:              programPath + "/v1/system/job-applications",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "列表查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobApplicationListForSystemReq{
					FacilityId: facilityId,
					JobId:      jobId,
				},
			},
			{
				SubName:           "按狀態查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobApplicationListForSystemReq{
					FacilityId: facilityId,
					JobId:      jobId,
					Status:     model.JobApplicationStatusApply,
				},
			},
			{
				SubName:           "狀態無效",
				ExpectErrRespCode: xresp.StatusBadRequest,
				Params: services.JobApplicationListForSystemReq{
					FacilityId: facilityId,
					JobId:      jobId,
					Status:     "INVALID_STATUS",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// TestJobApplicationInquire 測試工作申請詳情查詢
func TestJobApplicationSystemInquire(t *testing.T) {
	applicationId := uint64(2)
	test := xtest.Test{
		Url:              programPath + "/v1/system/job-applications/actions/inquire",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "詳情查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.JobApplicationInquireForSystemReq{
					JobApplicationId: applicationId,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSystemMyJobList(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/job-applications/actions/my-jobs",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "我的工作申請列表查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.MyJobListReqBySystem{
					//UserId: 15,
					//Status: model.JobApplicationStatusApply,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// endregion ------------------------------------------ 管理端 ------------------------------------------
