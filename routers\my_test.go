package routers

import (
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

func TestMyUserInfo(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/my/user-info",
		UserId:           15,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "獲取個人用戶信息",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestMyUserActionList(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/my/action-list",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "獲取用戶系統菜單權限",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestChangePassword(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/my/change-password",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "修改密碼",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常修改",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ChangePasswordReq{
					OldPassword: "123456",
					NewPassword: "123456",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
