package routers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xs3"
	"github.com/Norray/xrocket/xtest"
	"github.com/disintegration/imaging"
)

func TestProfessionalFileUploadFile(t *testing.T) {
	file, _ := os.Open("AHPRA.jpg")
	defer file.Close()

	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/professional-files/actions/upload-file",
		UserId:           8,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Form,
		Name:             "上傳",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ProfessionalFileUploadFileReq{
					FileCode: "AHPRA_CERT",
				},
				File:          file,
				FileFieldName: "file",
				CheckResultHandler: func(resp interface{}) bool {
					response := resp.(xresp.Response)
					var data services.ProfessionalFileUploadFileResp
					dataByte, _ := json.Marshal(response.Data)
					_ = json.Unmarshal(dataByte, &data)
					fmt.Println(data.ProfessionalFileId)
					return true
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

// TestFixThumbnails 修復縮略圖測試函數
func TestFixThumbnails(t *testing.T) {
	fmt.Println("=== 開始修復專業人士照片縮略圖 ===")

	// 初始化數據庫連接
	db := xgorm.DB
	if db == nil {
		t.Fatal("Failed to connect to database")
	}

	// 1. 獲取所有 DataType=APPROVED 的 Professional
	var professionals []model.Professional
	err := db.Where("data_type = ?", model.ProfessionalDataTypeApproved).Find(&professionals).Error
	if err != nil {
		t.Fatalf("Failed to query professionals: %v", err)
	}

	fmt.Printf("找到 %d 個已通過的專業人士\n", len(professionals))

	// 2. 查詢照片文件
	type PhotoFileInfo struct {
		ProfessionalId uint64 `json:"professionalId"`
		FileId         uint64 `json:"fileId"`
		ThumbnailPath  string `json:"thumbnailPath"`
		Bucket         string `json:"bucket"`
		UserId         uint64 `json:"userId"`
	}

	var photoFiles []PhotoFileInfo
	err = db.Table("professional p").
		Select("p.id as professional_id, pf.id as file_id, pf.thumbnail_path, pf.bucket, p.user_id").
		Joins("JOIN professional_file_relation pfr ON pfr.professional_id = p.id").
		Joins("JOIN professional_file pf ON pf.id = pfr.professional_file_id").
		Where("p.data_type = ?", model.ProfessionalDataTypeApproved).
		Where("pf.file_code <> ?", model.ProfessionalFileCodePhoto).
		Where("pf.thumbnail_path != ''").
		Scan(&photoFiles).Error

	if err != nil {
		t.Fatalf("Failed to query photo files: %v", err)
	}

	fmt.Printf("找到 %d 個照片文件需要處理\n", len(photoFiles))

	// 3. 處理每個照片文件
	successCount := 0
	errorCount := 0

	for i, photoFile := range photoFiles {
		fmt.Printf("\n[%d/%d] 處理專業人士 ID: %d, 文件 ID: %d\n",
			i+1, len(photoFiles), photoFile.ProfessionalId, photoFile.FileId)

		err := fixThumbnail(photoFile.Bucket, photoFile.ThumbnailPath)
		if err != nil {
			fmt.Printf("❌ 處理失敗: %v\n", err)
			errorCount++
		} else {
			fmt.Printf("✅ 處理成功\n")
			successCount++
		}

		// 避免過於頻繁的請求
		time.Sleep(100 * time.Millisecond)
	}

	fmt.Printf("\n=== 修復完成 ===\n")
	fmt.Printf("成功: %d, 失敗: %d, 總計: %d\n", successCount, errorCount, len(photoFiles))
}

// fixThumbnail 修復單個縮略圖
func fixThumbnail(bucket, thumbnailPath string) error {
	// 1. 從 OSS 下載原縮略圖
	downloadUrl, err := xs3.GetPresignDownloadUrl(bucket, thumbnailPath, time.Hour)
	if err != nil {
		return fmt.Errorf("failed to get download URL: %v", err)
	}
	fmt.Println("下載縮略圖 URL:", downloadUrl)
	// 2. 下載文件
	resp, err := http.Get(downloadUrl)
	if err != nil {
		return fmt.Errorf("failed to download file: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("download failed with status: %d", resp.StatusCode)
	}

	// 3. 讀取圖片數據
	imageData, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read image data: %v", err)
	}

	// 4. 解碼圖片
	img, err := imaging.Decode(bytes.NewReader(imageData))
	if err != nil {
		return fmt.Errorf("failed to decode image: %v", err)
	}

	// 5. 創建臨時文件路徑
	tempDir := "file-cache"
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return fmt.Errorf("failed to create temp directory: %v", err)
	}

	tempFile := filepath.Join(tempDir, fmt.Sprintf("temp_thumbnail_%d.jpg", time.Now().UnixNano()))
	defer os.Remove(tempFile) // 清理臨時文件

	// 6. 壓縮並保存圖片
	err = imaging.Save(img, tempFile, imaging.JPEGQuality(30))
	if err != nil {
		return fmt.Errorf("failed to save compressed image: %v", err)
	}

	// 7. 讀取壓縮後的文件
	compressedFile, err := os.Open(tempFile)
	if err != nil {
		return fmt.Errorf("failed to open compressed file: %v", err)
	}
	defer compressedFile.Close()

	// 8. 上傳覆蓋原文件
	err = xs3.UploadObjectFromReader(bucket, thumbnailPath, filepath.Base(thumbnailPath), compressedFile)
	if err != nil {
		return fmt.Errorf("failed to upload compressed image: %v", err)
	}

	return nil
}
