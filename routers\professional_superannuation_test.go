package routers

import (
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

var testProfessionalSuperannuationId uint64

func TestProfessionalSuperannuationEdit(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/superannuation/actions/edit",
		UserId:           76,
		UserIdWithDevice: true,
		Method:           xtest.Post,
		ParamsType:       xtest.Body,
		Name:             "修改",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.ProfessionalSuperannuationEditReq{
					ProfessionalSuperannuationId: 1,
					DataType:                     model.ProfessionalSuperannuationDataTypeDraft,
					SuperannuationType:           model.ProfessionalSuperannuationTypeDefaultSuperFund,
					FullName:                     "felix lili",
					TaxFileNumber:                "TFN123456789",
					DeclarationConfirmed:         "1",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestProfessionalSuperannuationInquire(t *testing.T) {
	// 使用debug運行才有顏色
	// 構建測試用例
	test := xtest.Test{
		Url:              programPath + "/v1/professional/superannuation/actions/inquire",
		UserId:           76,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "查詢",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常",
				ExpectErrRespCode: xresp.StatusOK,
				Params:            services.ProfessionalSuperannuationInquireReq{},
			},
		},
	}
	xtest.RunTests(t, test)
}
