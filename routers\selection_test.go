package routers

import (
	"testing"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xtest"
)

func TestSelectionPublicList(t *testing.T) {
	test := xtest.Test{
		Url:        programPath + "/v1/public/selections",
		Method:     xtest.Get,
		ParamsType: xtest.Query,
		Name:       "公開選項列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.SelectionListReqByPublic{
					SelectionTypes: "PROFESSIONAL_PROFESSION",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSelectionAppList(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/app/selections",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "App選項列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.SelectionListReqByApp{
					SelectionTypes: "PROFESSIONAL_PROFESSION",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSelectionProfessionalList(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/professional/selections",
		UserId:           8,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "專業人士選項列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.SelectionListReqByProfessional{
					SelectionTypes: "EXPERIENCE_LEVEL_MEDICAL_PRACTITIONER",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSelectionFacilityList(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/facility/selections",
		UserId:           5,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "機構選項列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.SelectionListReqByFacility{
					SelectionTypes: "PROFESSIONAL_PROFESSION",
					FacilityId:     1,
				},
			},
		},
	}
	xtest.RunTests(t, test)
}

func TestSelectionSystemList(t *testing.T) {
	test := xtest.Test{
		Url:              programPath + "/v1/system/selections",
		UserId:           1,
		UserIdWithDevice: true,
		Method:           xtest.Get,
		ParamsType:       xtest.Query,
		Name:             "系統選項列表",
		Cases: []xtest.TestCase{
			{
				SubName:           "正常查詢",
				ExpectErrRespCode: xresp.StatusOK,
				Params: services.SelectionListReq{
					SelectionTypes: "PROFESSIONAL_PROFESSION",
				},
			},
		},
	}
	xtest.RunTests(t, test)
}
