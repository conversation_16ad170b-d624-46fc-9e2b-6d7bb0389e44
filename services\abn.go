package services

import (
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
	"unicode"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xmodel/xtype"
	"github.com/Norray/xrocket/xtool"
	"github.com/PuerkitoBio/goquery"
	"gorm.io/gorm"
)

const (
	AbnStatusCancelled = "Cancelled" // 已取消
	AbnStatusActive    = "Active"    // 有效

	AbnZeroDate = "0001-01-01" // 空日期
)

var AbnService = new(abnService)

type abnService struct{}

func (a *abnService) NewHttpClient() *http.Client {
	// 創建帶代理的 HTTP 客戶端
	client := &http.Client{}
	transport := &http.Transport{}

	// 設置代理
	//transport.Proxy = func(req *http.Request) (*url.URL, error) {
	//	var proxyURL string
	//	if req.URL.Scheme == "https" {
	//		proxyURL = xconfig.ServerConf.HttpsProxy
	//	} else {
	//		proxyURL = xconfig.ServerConf.HttpProxy
	//	}
	//
	//	if proxyURL == "" {
	//		return nil, nil
	//	}
	//
	//	parsedURL, err := url.Parse(proxyURL)
	//	if err != nil {
	//		return nil, fmt.Errorf("failed to parse proxy URL: %v", err)
	//	}
	//	return parsedURL, nil
	//}

	client.Transport = transport

	return client
}

// AbnInfo ABN信息
type AbnInfo struct {
	AbnNumber     string `json:"abnNumber"`     // ABN號碼
	Status        string `json:"status"`        // ABN狀態 Cancelled Active
	AbnStatus     string `json:"abnStatus"`     // ABN狀態 Cancelled from 30 Mar 2012 / Active from 01 Nov 1999
	AbnStatusDate string `json:"abnStatusDate"` // ABN狀態日期
	EntityName    string `json:"entityName"`    // 實體名稱
	EntityType    string `json:"entityType"`    // 實體類型
}

var ABNNotFoundError = fmt.Errorf("ABN: not found")

// 獲取ABN信息
func (a *abnService) GetAbnInfo(abn string) (AbnInfo, error) {
	var resp AbnInfo
	var err error

	client := a.NewHttpClient()

	// 使用url.Values編碼ABN參數防止特殊字符問題
	params := url.Values{}
	params.Add("abn", abn)
	reqURL := "https://abr.business.gov.au/ABN/View" + "?" + params.Encode()
	httpResp, err := client.Get(reqURL)
	if err != nil {
		return resp, fmt.Errorf("request abn info failed: %v", err)
	}
	defer httpResp.Body.Close()

	if httpResp.StatusCode != http.StatusOK {
		return resp, fmt.Errorf("http status code is not 200: %d %s", httpResp.StatusCode, httpResp.Status)
	}

	// 載入 HTML 文檔
	doc, err := goquery.NewDocumentFromReader(httpResp.Body)
	if err != nil {
		return resp, fmt.Errorf("failed to load html document: %v", err)
	}

	// 獲取input的值 ： input id="ABN"
	abnInput, ok := doc.Find("input#ABN").First().Attr("value")
	if !ok || abnInput != abn {
		return resp, ABNNotFoundError
	}
	resp.AbnNumber = abnInput
	trs := doc.Find("table > tbody > tr")
	trs.Each(func(i int, s *goquery.Selection) {
		th := s.Find("th").First()
		label := strings.ToLower(a.Trim(th.Text()))
		label = strings.Replace(label, ":", "", 1)
		switch label {
		case "abn status":
			// Cancelled from 30 Mar 2012
			// Active from 01 Nov 1999
			resp.AbnStatus = a.Trim(s.Find("td").First().Text())
			statusStr := strings.ToLower(resp.AbnStatus)
			if strings.Contains(statusStr, "cancelled") {
				resp.Status = AbnStatusCancelled
			} else if strings.Contains(statusStr, "active") {
				resp.Status = AbnStatusActive
			}
			// 找到from後面的日期
			fromIndex := strings.Index(statusStr, "from")
			if fromIndex != -1 {
				datePart := strings.TrimSpace(statusStr[fromIndex+5:])
				resp.AbnStatusDate, err = a.ConvertDate(datePart)
				if err != nil {
					err = fmt.Errorf("ABN status date convert failed: %v", err)
					return // 退出当前回调的迭代
				}
			}
		case "entity name":
			resp.EntityName = a.Trim(s.Find("td").First().Text())
		case "entity type":
			resp.EntityType = a.Trim(s.Find("td").First().Text())
		}
	})
	// 添加错误检查
	if err != nil {
		return resp, err
	}

	return resp, nil
}

// 去除前後空白、換行符號
func (a *abnService) Trim(str string) string {
	return a.CleanSpaces(strings.TrimSpace(strings.ReplaceAll(str, "\n", "")))
}

// CleanSpaces 將字串中所有特殊空白符（例如：non-breaking space、全形空格）轉換為普通空格
func (a *abnService) CleanSpaces(s string) string {
	return strings.Map(func(r rune) rune {
		switch r {
		case '\u00A0', // non-breaking space
			'\u2002', // en space
			'\u2003', // em space
			'\u2009', // thin space
			'\u200B', // zero width space
			'\u3000': // full-width space
			return ' '
		default:
			if unicode.IsSpace(r) {
				return ' '
			}
			return r
		}
	}, s)
}

// 轉換日期格式 30 Mar 2012 >> 2012-03-30
func (a *abnService) ConvertDate(dateStr string) (string, error) {
	t, err := time.Parse(xtool.DateDayE3, dateStr)
	if err != nil {
		return "", err
	}
	return t.Format(xtool.DateDayA), nil
}

// ABR XML API 相關結構體
type ABRPayloadSearchResults struct {
	XMLName xml.Name `xml:"ABRPayloadSearchResults"`
	Request struct {
		IdentifierSearchRequest struct {
			AuthenticationGUID string `xml:"authenticationGUID"`
			IdentifierType     string `xml:"identifierType"`
			IdentifierValue    string `xml:"identifierValue"`
			History            string `xml:"history"`
		} `xml:"identifierSearchRequest"`
	} `xml:"request"`
	Response struct {
		UsageStatement          string `xml:"usageStatement"`
		DateRegisterLastUpdated string `xml:"dateRegisterLastUpdated"`
		DateTimeRetrieved       string `xml:"dateTimeRetrieved"`
		Exception               struct {
			ExceptionDescription string `xml:"exceptionDescription"`
			ExceptionCode        string `xml:"exceptionCode"`
		} `xml:"exception"`
		BusinessEntity202001 struct {
			RecordLastUpdatedDate string `xml:"recordLastUpdatedDate"`
			ABN                   struct {
				IdentifierValue    string `xml:"identifierValue"`
				IsCurrentIndicator string `xml:"isCurrentIndicator"`
				ReplacedFrom       string `xml:"replacedFrom"`
			} `xml:"ABN"`
			EntityStatus []struct {
				EntityStatusCode string `xml:"entityStatusCode"`
				EffectiveFrom    string `xml:"effectiveFrom"`
				EffectiveTo      string `xml:"effectiveTo"`
			} `xml:"entityStatus"`
			ASICNumber string `xml:"ASICNumber"`
			EntityType struct {
				EntityTypeCode    string `xml:"entityTypeCode"`
				EntityDescription string `xml:"entityDescription"`
			} `xml:"entityType"`
			GoodsAndServicesTax []struct {
				EffectiveFrom string `xml:"effectiveFrom"`
				EffectiveTo   string `xml:"effectiveTo"`
			} `xml:"goodsAndServicesTax"`
			LegalName []struct {
				GivenName      string `xml:"givenName"`
				OtherGivenName string `xml:"otherGivenName"`
				FamilyName     string `xml:"familyName"`
				EffectiveFrom  string `xml:"effectiveFrom"`
				EffectiveTo    string `xml:"effectiveTo"`
			} `xml:"legalName"`
			MainTradingName []struct {
				OrganisationName string `xml:"organisationName"`
				EffectiveFrom    string `xml:"effectiveFrom"`
				EffectiveTo      string `xml:"effectiveTo"`
			} `xml:"mainTradingName"`
			MainBusinessPhysicalAddress []struct {
				StateCode     string `xml:"stateCode"`
				Postcode      string `xml:"postcode"`
				EffectiveFrom string `xml:"effectiveFrom"`
				EffectiveTo   string `xml:"effectiveTo"`
			} `xml:"mainBusinessPhysicalAddress"`
			BusinessName []struct {
				OrganisationName string `xml:"organisationName"`
				EffectiveFrom    string `xml:"effectiveFrom"`
				EffectiveTo      string `xml:"effectiveTo"`
			} `xml:"businessName"`
		} `xml:"businessEntity202001"`
	} `xml:"response"`
}

// ABN詳細信息查詢請求
type AbnDetailQueryReq struct {
	AbnNumber          string // ABN號碼
	AuthenticationGuid string // 認證GUID
	IncludeHistorical  bool   // 是否包含歷史記錄
}

// ABN詳細信息查詢響應
type AbnDetailQueryResp struct {
	// 基本信息
	AbnNumber               string `json:"abnNumber"`               // ABN號碼
	RecordLastUpdatedDate   string `json:"recordLastUpdatedDate"`   // 記錄最後更新日期
	DateRegisterLastUpdated string `json:"dateRegisterLastUpdated"` // 註冊最後更新日期
	DateTimeRetrieved       string `json:"dateTimeRetrieved"`       // 檢索時間

	// ABN信息
	AbnInfo AbnInfo `json:"abnInfo"` // ABN信息

	// 實體狀態
	EntityStatuses []AbnEntityStatus `json:"entityStatuses"` // 實體狀態記錄

	// ASIC號碼
	AsicNumber string `json:"asicNumber"` // ASIC號碼

	// 實體類型
	EntityType AbnEntityType `json:"entityType"` // 實體類型

	// GST記錄
	GstRecords []AbnGstRecord `json:"gstRecords"` // GST記錄

	// 法定名稱
	LegalNames []AbnLegalName `json:"legalNames"` // 法定名稱記錄

	// 主要交易名稱
	MainTradingNames []AbnTradingName `json:"mainTradingNames"` // 主要交易名稱記錄

	// 主要營業地址
	MainBusinessPhysicalAddresses []AbnBusinessAddress `json:"mainBusinessPhysicalAddresses"` // 主要營業地址記錄

	// 營業名稱
	BusinessNames []AbnTradingName `json:"businessNames"` // 營業名稱記錄
}

// ABN基本信息
type AbnBasicInfo struct {
	IdentifierValue    string `json:"identifierValue"`    // ABN值
	IsCurrentIndicator string `json:"isCurrentIndicator"` // 是否當前有效
	ReplacedFrom       string `json:"replacedFrom"`       // 替換開始日期
}

// ABN實體狀態
type AbnEntityStatus struct {
	EntityStatusCode string `json:"entityStatusCode"` // 實體狀態代碼
	EffectiveFrom    string `json:"effectiveFrom"`    // 生效日期
	EffectiveTo      string `json:"effectiveTo"`      // 失效日期
}

// ABN實體類型
type AbnEntityType struct {
	EntityTypeCode    string `json:"entityTypeCode"`    // 實體類型代碼
	EntityDescription string `json:"entityDescription"` // 實體類型描述
}

// ABN GST記錄
type AbnGstRecord struct {
	EffectiveFrom string `json:"effectiveFrom"` // 生效日期
	EffectiveTo   string `json:"effectiveTo"`   // 失效日期
}

// ABN法定名稱
type AbnLegalName struct {
	GivenName      string `json:"givenName"`      // 名字
	OtherGivenName string `json:"otherGivenName"` // 其他名字
	FamilyName     string `json:"familyName"`     // 姓氏
	EffectiveFrom  string `json:"effectiveFrom"`  // 生效日期
	EffectiveTo    string `json:"effectiveTo"`    // 失效日期
}

// ABN交易名稱
type AbnTradingName struct {
	OrganisationName string `json:"organisationName"` // 組織名稱
	EffectiveFrom    string `json:"effectiveFrom"`    // 生效日期
	EffectiveTo      string `json:"effectiveTo"`      // 失效日期
}

// ABN營業地址
type AbnBusinessAddress struct {
	StateCode     string `json:"stateCode"`     // 州代碼
	Postcode      string `json:"postcode"`      // 郵編
	EffectiveFrom string `json:"effectiveFrom"` // 生效日期
	EffectiveTo   string `json:"effectiveTo"`   // 失效日期
}

// 從ABR API查詢ABN詳細信息
func (a *abnService) QueryAbnDetailFromAPI(req AbnDetailQueryReq) (AbnDetailQueryResp, error) {
	var resp AbnDetailQueryResp

	client := a.NewHttpClient()

	// 構建API URL
	apiURL := "https://abr.business.gov.au/abrxmlsearch/AbrXmlSearch.asmx/SearchByABNv202001"
	params := url.Values{}
	params.Add("searchString", req.AbnNumber)
	params.Add("authenticationGuid", req.AuthenticationGuid)
	if req.IncludeHistorical {
		params.Add("includeHistoricalDetails", "Y")
	} else {
		params.Add("includeHistoricalDetails", "N")
	}

	reqURL := apiURL + "?" + params.Encode()

	// 發送HTTP請求
	httpResp, err := client.Get(reqURL)
	if err != nil {
		return resp, fmt.Errorf("request ABN detail failed: %v", err)
	}
	defer httpResp.Body.Close()

	if httpResp.StatusCode != http.StatusOK {
		return resp, fmt.Errorf("http status code is not 200: %d %s", httpResp.StatusCode, httpResp.Status)
	}

	// 讀取響應內容
	body, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return resp, fmt.Errorf("failed to read response body: %v", err)
	}

	// 解析XML
	var xmlResp ABRPayloadSearchResults
	if err := xml.Unmarshal(body, &xmlResp); err != nil {
		return resp, fmt.Errorf("failed to parse XML response: %v", err)
	}

	// 檢查是否有異常
	if xmlResp.Response.Exception.ExceptionDescription != "" {
		if xmlResp.Response.Exception.ExceptionDescription == "Search text is not a valid ABN or ACN" {
			return resp, ABNNotFoundError
		}
		return resp, fmt.Errorf("ABN query failed: %s (Code: %s)",
			xmlResp.Response.Exception.ExceptionDescription,
			xmlResp.Response.Exception.ExceptionCode)
	}

	// 轉換數據
	resp.AbnNumber = req.AbnNumber
	resp.RecordLastUpdatedDate = xmlResp.Response.BusinessEntity202001.RecordLastUpdatedDate
	resp.DateRegisterLastUpdated = xmlResp.Response.DateRegisterLastUpdated
	resp.DateTimeRetrieved = xmlResp.Response.DateTimeRetrieved

	// 轉換ABN基本信息
	resp.AbnInfo = AbnInfo{
		AbnNumber:     xmlResp.Response.BusinessEntity202001.ABN.IdentifierValue,
		Status:        xmlResp.Response.BusinessEntity202001.ABN.IsCurrentIndicator,
		AbnStatus:     xmlResp.Response.BusinessEntity202001.ABN.IsCurrentIndicator,
		AbnStatusDate: xmlResp.Response.BusinessEntity202001.ABN.ReplacedFrom,
	}

	// 轉換實體狀態記錄
	for _, status := range xmlResp.Response.BusinessEntity202001.EntityStatus {
		resp.EntityStatuses = append(resp.EntityStatuses, AbnEntityStatus{
			EntityStatusCode: status.EntityStatusCode,
			EffectiveFrom:    status.EffectiveFrom,
			EffectiveTo:      status.EffectiveTo,
		})
	}

	// 轉換ASIC號碼
	resp.AsicNumber = xmlResp.Response.BusinessEntity202001.ASICNumber

	// 轉換實體類型
	resp.EntityType = AbnEntityType{
		EntityTypeCode:    xmlResp.Response.BusinessEntity202001.EntityType.EntityTypeCode,
		EntityDescription: xmlResp.Response.BusinessEntity202001.EntityType.EntityDescription,
	}

	// 轉換GST記錄
	for _, gst := range xmlResp.Response.BusinessEntity202001.GoodsAndServicesTax {
		resp.GstRecords = append(resp.GstRecords, AbnGstRecord{
			EffectiveFrom: gst.EffectiveFrom,
			EffectiveTo:   gst.EffectiveTo,
		})
	}

	// 轉換法定名稱記錄
	for _, legalName := range xmlResp.Response.BusinessEntity202001.LegalName {
		resp.LegalNames = append(resp.LegalNames, AbnLegalName{
			GivenName:      legalName.GivenName,
			OtherGivenName: legalName.OtherGivenName,
			FamilyName:     legalName.FamilyName,
			EffectiveFrom:  legalName.EffectiveFrom,
			EffectiveTo:    legalName.EffectiveTo,
		})
	}

	// 轉換主要交易名稱記錄
	for _, tradingName := range xmlResp.Response.BusinessEntity202001.MainTradingName {
		resp.MainTradingNames = append(resp.MainTradingNames, AbnTradingName{
			OrganisationName: tradingName.OrganisationName,
			EffectiveFrom:    tradingName.EffectiveFrom,
			EffectiveTo:      tradingName.EffectiveTo,
		})
	}

	// 轉換主要營業地址記錄
	for _, address := range xmlResp.Response.BusinessEntity202001.MainBusinessPhysicalAddress {
		resp.MainBusinessPhysicalAddresses = append(resp.MainBusinessPhysicalAddresses, AbnBusinessAddress{
			StateCode:     address.StateCode,
			Postcode:      address.Postcode,
			EffectiveFrom: address.EffectiveFrom,
			EffectiveTo:   address.EffectiveTo,
		})
	}

	// 轉換營業名稱記錄
	for _, businessName := range xmlResp.Response.BusinessEntity202001.BusinessName {
		resp.BusinessNames = append(resp.BusinessNames, AbnTradingName{
			OrganisationName: businessName.OrganisationName,
			EffectiveFrom:    businessName.EffectiveFrom,
			EffectiveTo:      businessName.EffectiveTo,
		})
	}

	return resp, nil
}

// 根據ABN查詢結果更新專業人士GST記錄
func (a *abnService) UpdateProfessionalGstFromAbn(db *gorm.DB, userId uint64, professionalId uint64, abnResp AbnDetailQueryResp) error {
	// 解析記錄更新日期
	recordUpdatedDate, err := a.parseDate(abnResp.RecordLastUpdatedDate)
	if err != nil {
		return fmt.Errorf("failed to parse record updated date: %v", err)
	}

	// 刪除該專業人士的舊GST記錄
	if err := db.Where("user_id = ? AND professional_id = ?", userId, professionalId).Delete(&model.ProfessionalGST{}).Error; err != nil {
		return fmt.Errorf("failed to delete old GST records: %v", err)
	}

	// 創建新的GST記錄
	for _, gstRecord := range abnResp.GstRecords {
		// 解析生效日期
		effectiveFrom, err := a.parseDate(gstRecord.EffectiveFrom)
		if err != nil {
			return fmt.Errorf("failed to parse effective from date: %v", err)
		}

		// 解析失效日期
		var effectiveTo xtype.NullDate
		if a.IsValidDate(gstRecord.EffectiveTo) {
			effectiveToDate, err := a.parseDate(gstRecord.EffectiveTo)
			if err != nil {
				return fmt.Errorf("failed to parse effective to date: %v", err)
			}
			effectiveTo = xtype.NewNullDate(effectiveToDate.String())
		}

		// 創建GST記錄
		gst := model.ProfessionalGST{
			UserId:            userId,
			ProfessionalId:    professionalId,
			EffectiveFrom:     xtype.NewDate(effectiveFrom.String()),
			EffectiveTo:       effectiveTo,
			RecordUpdatedDate: xtype.NewDate(recordUpdatedDate.String()),
			UpdateTime:        time.Now().UTC(),
		}

		if err := db.Create(&gst).Error; err != nil {
			return fmt.Errorf("failed to create GST record: %v", err)
		}
	}

	return nil
}

// 解析日期字符串為xtype.Date
func (a *abnService) parseDate(dateStr string) (xtype.NullDate, error) {
	if !a.IsValidDate(dateStr) {
		return xtype.NewNullDate(), nil
	}
	return xtype.NewNullDate(dateStr), nil
}

// 判斷是否有效日期
func (a *abnService) IsValidDate(dateStr string) bool {
	if dateStr == "" || dateStr == AbnZeroDate {
		return false
	}
	return true
}

// 更新專業人士ABN相關信息
func (a *abnService) UpdateProfessionalAbnInfo(db *gorm.DB, professionalId uint64, abnResp AbnDetailQueryResp) error {
	// 獲取當前最新的實體狀態
	var currentEntityStatus string
	var currentEntityName string
	var currentEntityType string

	// 找到最新的實體狀態（effectiveTo為空或最晚的）
	for _, status := range abnResp.EntityStatuses {
		if !a.IsValidDate(status.EffectiveTo) {
			currentEntityStatus = status.EntityStatusCode
			break
		}
	}

	// 獲取當前有效的法定名稱
	for _, legalName := range abnResp.LegalNames {
		if !a.IsValidDate(legalName.EffectiveTo) {
			if legalName.GivenName != "" && legalName.FamilyName != "" {
				currentEntityName = legalName.GivenName + " " + legalName.FamilyName
			}
			break
		}
	}

	// 如果沒有法定名稱，嘗試使用主要交易名稱
	if currentEntityName == "" {
		for _, tradingName := range abnResp.MainTradingNames {
			if !a.IsValidDate(tradingName.EffectiveTo) {
				currentEntityName = tradingName.OrganisationName
				break
			}
		}
	}

	// 獲取實體類型
	currentEntityType = abnResp.EntityType.EntityDescription

	// 確定ABN有效性
	abnValid := model.ProfessionalAbnValidStatusN
	if currentEntityStatus == AbnStatusActive {
		abnValid = model.ProfessionalAbnValidStatusY
	}

	// 更新Professional表
	updates := map[string]interface{}{
		"abn_valid":       abnValid,
		"abn_entity_name": currentEntityName,
		"abn_entity_type": currentEntityType,
		"update_time":     time.Now().UTC(),
	}

	if err := db.Model(&model.Professional{}).Where("id = ?", professionalId).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update professional ABN info: %v", err)
	}

	return nil
}

// 查詢ABN信息並更新專業人士所有相關數據
type QueryAndUpdateProfessionalDataAbnReq struct {
	UserId            uint64 // 用戶ID
	ProfessionalId    uint64 // 專業人士ID
	AbnNumber         string // ABN號碼
	IncludeHistorical bool   // 是否包含歷史記錄
}

func (a *abnService) GetAbnInfoByApi(db *gorm.DB, abnNumber string) (AbnDetailQueryResp, error) {
	var emptyResp AbnDetailQueryResp
	guid, err := CommonSettingService.GetSettingValueByCode(db, model.CommonSettingCodeAbnGuid)
	if err != nil {
		return emptyResp, fmt.Errorf("failed to get ABN GUID: %v", err)
	}
	abnResp, err := a.QueryAbnDetailFromAPI(AbnDetailQueryReq{
		AbnNumber:          abnNumber,
		AuthenticationGuid: guid,
		IncludeHistorical:  false,
	})
	if err != nil {
		return emptyResp, err
	}
	return abnResp, nil
}

func (a *abnService) QueryAndUpdateProfessionalAbnData(db *gorm.DB, req QueryAndUpdateProfessionalDataAbnReq) (AbnDetailQueryResp, error) {
	var emptyResp AbnDetailQueryResp
	guid, err := CommonSettingService.GetSettingValueByCode(db, model.CommonSettingCodeAbnGuid)
	if err != nil {
		return emptyResp, fmt.Errorf("failed to get ABN GUID: %v", err)
	}

	// 1. 從API查詢ABN詳細信息
	abnResp, err := a.QueryAbnDetailFromAPI(AbnDetailQueryReq{
		AbnNumber:          req.AbnNumber,
		AuthenticationGuid: guid,
		IncludeHistorical:  req.IncludeHistorical,
	})
	if err != nil {
		return emptyResp, fmt.Errorf("failed to query ABN detail: %v", err)
	}

	// 2. 更新Professional表中的ABN信息
	// if err := a.UpdateProfessionalAbnInfo(db, req.ProfessionalId, abnResp); err != nil {
	// 	return emptyResp, fmt.Errorf("failed to update professional ABN info: %v", err)
	// }

	// 3. 更新GST記錄
	if err := a.UpdateProfessionalGstFromAbn(db, req.UserId, req.ProfessionalId, abnResp); err != nil {
		return emptyResp, fmt.Errorf("failed to update professional GST: %v", err)
	}

	return abnResp, nil
}

// 獲取ABN狀態
func (a *abnService) getAbnInfo(abnResp AbnDetailQueryResp) AbnInfo {
	var info AbnInfo
	info.AbnNumber = abnResp.AbnNumber
	if len(abnResp.EntityStatuses) > 0 {
		entityStatus := abnResp.EntityStatuses[0]
		info.Status = entityStatus.EntityStatusCode
		info.AbnStatus = entityStatus.EntityStatusCode
		if info.AbnStatus == AbnStatusActive {
			info.AbnStatusDate = entityStatus.EffectiveFrom
		} else if info.AbnStatus == AbnStatusCancelled {
			info.AbnStatusDate = entityStatus.EffectiveTo
		}
	}
	if len(abnResp.LegalNames) > 0 {
		legalName := abnResp.LegalNames[0]
		arr := make([]string, 0)
		if legalName.FamilyName != "" {
			arr = append(arr, legalName.FamilyName)
		}
		if legalName.GivenName != "" {
			arr = append(arr, legalName.GivenName)
		}
		info.EntityName = strings.Join(arr, ", ")
		if legalName.OtherGivenName != "" {
			info.EntityName += " " + legalName.OtherGivenName
		}
	}
	info.EntityType = abnResp.EntityType.EntityDescription

	return info
}

func (a *abnService) FormatAbn(abn string) string {
	abn = strings.ReplaceAll(abn, " ", "") // 移除空格
	n := len(abn)

	// 預估空間：原字串長度 + 3 個空格 (最多)
	var b strings.Builder
	b.Grow(n + (n-1)/3)

	if n == 11 {
		// 固定 ABN 格式 2-3-3-3
		b.WriteString(abn[0:2])
		b.WriteByte(' ')
		b.WriteString(abn[2:5])
		b.WriteByte(' ')
		b.WriteString(abn[5:8])
		b.WriteByte(' ')
		b.WriteString(abn[8:11])
		return b.String()
	}

	// 容錯：非 11 位 → 每 3 位一組
	for i := 0; i < n; i++ {
		if i > 0 && i%3 == 0 {
			b.WriteByte(' ')
		}
		b.WriteByte(abn[i])
	}

	return b.String()
}
