package services

import (
	"encoding/json"
	"errors"
	"io"
	"mime/multipart"

	"github.com/Norray/xrocket/xgemini"
	"github.com/Norray/xrocket/xmodel"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

var AiService = new(aiService)

type aiService struct{}

type AiDateAndNumber struct {
	AiResp
	ExpireDate string `json:"expireDate"` // 到期日
	IssueDate  string `json:"issueDate"`  // 到期日
	Number     string `json:"number"`
}

type AiResp struct {
	Model       string `json:"model"`
	Output      string `json:"output"`
	InputToken  int32  `json:"inputToken"`
	OutputToken int32  `json:"outputToken"`
}

// AI識別圖片的到期日
func (s *aiService) AiDateAndNumber(db *gorm.DB, file *multipart.FileHeader) (AiDateAndNumber, error) {
	var err error
	var resp AiDateAndNumber
	// 讀取文件
	reader, err := file.Open()
	if err != nil {
		return resp, err
	}
	defer reader.Close()

	// 讀取文件
	fileData, err := io.ReadAll(reader)
	if err != nil {
		return resp, err
	}
	aiResp, err := AiService.SendAiDateAndNumber(db, file.Filename, fileData)
	if err != nil {
		return resp, err
	}
	_ = copier.Copy(&resp.AiResp, aiResp)
	if aiResp.Output != "" {
		var data map[string]string
		err = json.Unmarshal([]byte(aiResp.Output), &data)
		if err != nil {
			return resp, err
		}
		resp.ExpireDate = data["expireDate"]
		resp.IssueDate = data["issueDate"]
		resp.Number = data["number"]
	}
	return resp, nil
}

func (s *aiService) SendAiDateAndNumber(db *gorm.DB, filename string, data []byte) (AiResp, error) {
	prompt, err := s.GetAIDateAndNumberPrompt(db)
	if err != nil {
		return AiResp{}, err
	}
	if prompt == "" {
		return AiResp{}, errors.New("prompt is not set")
	}
	modelName := "gemini-2.0-flash-001"
	result, err := xgemini.GeminiChatCompletions(&xgemini.GeminiChatCompletionsReq{
		Prompt:          prompt,
		ModelName:       modelName,
		Temperature:     0.7,
		TopK:            1,
		TopP:            0,
		MaxOutputTokens: xgemini.GeminiDefaultMaxOutputTokens,
		File: []*xgemini.GeminiFile{
			{
				FileName: filename,
				MimeType: xgemini.GetFileMimeType(filename),
				Content:  data,
			},
		},
	})
	if err != nil {
		return AiResp{}, err
	}
	return AiResp{
		Model:       modelName,
		Output:      xgemini.RemoveJSONMarkdown(result.Output),
		InputToken:  result.InputToken,
		OutputToken: result.OutputToken,
	}, nil
}

func (s *aiService) GetAIDateAndNumberPrompt(db *gorm.DB) (string, error) {
	var setting xmodel.CommonSetting
	if err := db.Where("code = ?", "AI_DATE_AND_NUMBER_PROMPT").First(&setting).Error; err != nil {
		return "", err
	}
	return setting.SettingValue, nil
}
