package services

import (
	"fmt"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gorm.io/gorm"
)

var BenefitService = new(benefitService)

type benefitService struct{}

func (s *benefitService) CheckIdExist(db *gorm.DB, m *model.Benefit, id uint64, facilityId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.benefit.id.does_not_exist",
		Other: "No such record, please try after reloading.",
	}
	var err error
	if err = db.Where("facility_id = ?", facilityId).Where("id = ?", id).First(&m).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

type BenefitCreateReq struct {
	FacilityId uint64 `json:"facilityId" binding:"required"`
	Name       string `json:"name" binding:"required"`
	Status     string `json:"status" binding:"required,oneof=ENABLE DISABLE"`
}

type BenefitCreateResp struct {
	BenefitId uint64 `json:"benefitId"`
}

func (s *benefitService) Create(db *gorm.DB, req BenefitCreateReq) (BenefitCreateResp, error) {
	var resp BenefitCreateResp
	var err error
	var m model.Benefit
	_ = copier.Copy(&m, req)
	if err = db.Create(&m).Error; err != nil {
		return resp, err
	}
	resp.BenefitId = m.Id
	return resp, nil
}

type BenefitListReq struct {
	FacilityId uint64 `form:"facilityId" binding:"required"`
	Name       string `form:"name"`
	Status     string `form:"status"`
}

type BenefitListResp struct {
	BenefitId  uint64 `json:"benefitId"`
	FacilityId uint64 `json:"facilityId"`
	Name       string `json:"name"`
	Status     string `json:"status"`
}

func (s *benefitService) List(db *gorm.DB, req BenefitListReq, pageSet *xresp.PageSet) ([]BenefitListResp, error) {
	var err error
	var resp []BenefitListResp
	builder := db.Table("benefit AS b").Select([]string{
		"b.id AS benefit_id",
		"b.facility_id",
		"b.name",
		"b.status",
	}).Where("b.facility_id = ?", req.FacilityId)

	if req.Name != "" {
		builder = builder.Where("b.name LIKE ?", xgorm.EscapeLikeWithWildcards(req.Name))
	}
	if req.Status != "" {
		builder = builder.Where("b.status = ?", req.Status)
	}
	if err = builder.Scopes(xresp.Paginate(pageSet)).Order("b.id").Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

type BenefitSearchReq struct {
	FacilityId uint64 `form:"facilityId" binding:"required"`
	Name       string `form:"name"`
	Status     string `form:"status" binding:"required,oneof=ENABLE DISABLE"`
	SelectedId uint64 `form:"selectedId"`
	Limit      int    `form:"limit"`
}

type BenefitSearchResp struct {
	BenefitId  uint64 `json:"benefitId"`
	FacilityId uint64 `json:"facilityId"`
	Name       string `json:"name"`
	Status     string `json:"status"`
}

func (s *benefitService) Search(db *gorm.DB, req BenefitSearchReq) ([]BenefitSearchResp, error) {
	var err error
	var resp []BenefitSearchResp
	builder := db.Table("benefit AS b").Select([]string{
		"b.id AS benefit_id",
		"b.facility_id",
		"b.name",
		"b.status",
	}).Where("b.facility_id = ?", req.FacilityId)

	if req.Name != "" {
		builder = builder.Where("b.name LIKE ?", xgorm.EscapeLikeWithWildcards(req.Name))
	}
	if req.Status != "" {
		builder = builder.Where("b.status = ?", req.Status)
	}
	if req.SelectedId != 0 {
		builder = builder.Order(fmt.Sprintf("IF(b.id = %d,0,1)", req.SelectedId))
	}
	if req.Limit > 0 {
		builder = builder.Limit(req.Limit)
	}
	if err = builder.Order("b.id").Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

type BenefitEditReq struct {
	FacilityId uint64 `json:"facilityId" binding:"required"`
	BenefitId  uint64 `json:"benefitId" binding:"required"`
	Name       string `json:"name" binding:"required"`
	Status     string `json:"status" binding:"required,oneof=ENABLE DISABLE"`
}

func (s *benefitService) Edit(db *gorm.DB, req BenefitEditReq) error {
	var err error
	var m model.Benefit
	if err = db.
		Where("facility_id = ?", req.FacilityId).
		Where("id = ?", req.BenefitId).
		First(&m).Error; err != nil {
		return err
	}
	_ = copier.Copy(&m, req)
	if err = db.Save(&m).Error; err != nil {
		return err
	}
	return nil
}

type BenefitInquireReq struct {
	FacilityId uint64 `form:"facilityId" binding:"required"`
	BenefitId  uint64 `form:"benefitId" binding:"required"`
}

type BenefitInquireResp struct {
	BenefitId  uint64 `json:"benefitId"`
	FacilityId uint64 `json:"facilityId"`
	Name       string `json:"name"`
	Status     string `json:"status"`
}

func (s *benefitService) Inquire(db *gorm.DB, req BenefitInquireReq) (BenefitInquireResp, error) {
	var err error
	var resp BenefitInquireResp
	var m model.Benefit
	if err = db.First(&m, req.BenefitId).Error; err != nil {
		return resp, err
	}
	_ = copier.Copy(&resp, m)
	resp.BenefitId = m.Id
	return resp, nil
}

type BenefitDeleteReq struct {
	FacilityId uint64 `json:"facilityId" binding:"required"`
	BenefitId  uint64 `json:"benefitId" binding:"required"`
}

func (s *benefitService) Delete(db *gorm.DB, req BenefitDeleteReq) error {
	var err error
	if err = db.Where("facility_id = ?", req.FacilityId).Delete(&model.Benefit{}, req.BenefitId).Error; err != nil {
		return err
	}
	return nil
}

func (s *benefitService) Init(db *gorm.DB, facilityId uint64) error {
	var err error
	defaultBenefits := []string{
		"Travel Reimbursement",
		"Accommodation Provided",
		"Meal Allowance",
		"Onsite Café",
		"Free Onsite Parking",
	}

	initItem := make([]model.Benefit, 0)
	for _, name := range defaultBenefits {
		initItem = append(initItem, model.Benefit{
			FacilityId: facilityId,
			Name:       name,
			Status:     model.BenefitStatusEnable,
		})
	}
	if err = db.CreateInBatches(initItem, 10).Error; err != nil {
		return err
	}
	return nil
}
