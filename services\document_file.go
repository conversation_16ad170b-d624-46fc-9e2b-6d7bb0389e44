package services

import (
	"fmt"
	"mime"
	"mime/multipart"
	"path"
	"path/filepath"
	"strings"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xs3"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

var DocumentFileService = new(documentFileService)

type documentFileService struct{}

func (s *documentFileService) CheckIdExist(db *gorm.DB, m *model.DocumentFile, id uint64, userId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.document_file.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	var err error
	if err = db.Where("user_id = ?", userId).First(&m, id).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

func (s *documentFileService) CheckIdExistByFacilityId(db *gorm.DB, m *model.DocumentFile, id uint64, facilityId ...uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.document_file.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	var err error
	builder := db.Table("document_file AS df").
		Select("df.*").
		Joins("JOIN document_file_relation AS dfr ON dfr.document_file_id = df.id").
		Joins("JOIN document AS d ON d.id = dfr.document_id").
		Where("df.id = ?", id)
	if len(facilityId) > 0 {
		builder = builder.Where("d.facility_id = ?", facilityId[0])
	}
	err = builder.First(&m).Error
	if xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

func (s *documentFileService) CheckIdsExist(db *gorm.DB, ids []uint64, userId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.document_file.ids.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	var err error
	var m []model.DocumentFile
	if err = db.Where("id IN (?)", ids).Where("user_id = ?", userId).Find(&m).Error; err != nil {
		return false, msg, err
	}
	if len(m) != len(ids) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

func (s *documentFileService) CheckFileCodeExist(fileCode string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.document_file.code.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	for _, code := range model.DocumentFileCodes {
		if code == fileCode {
			return true, i18n.Message{}, nil
		}
	}
	return false, msg, nil
}

// 檢查文件大小是否符合限制
func (s *documentFileService) CheckFileSize(fileCode string, fileSize int64) (bool, i18n.Message, map[string]string, error) {
	limitations := model.DocumentFile{}.FileSizeLimitations()
	maxSize, exists := limitations[fileCode]
	if !exists {
		// FileCode 不存在於限制列表中
		return false, model.MsgFileCodeNotExist, nil, nil
	}

	if fileSize > maxSize {
		templateData := map[string]string{
			"MaxSize": fmt.Sprintf("%d", maxSize/(1024*1024)),
		}
		return false, model.MsgFileSizeTooLarge, templateData, nil
	}
	return true, i18n.Message{}, nil, nil
}

// 檢查文件類型是否允許
func (s *documentFileService) CheckFileType(fileCode string, fileType string) (bool, i18n.Message, map[string]string, error) {
	allowedTypes := model.DocumentFile{}.AllowedFileTypes()
	types, exists := allowedTypes[fileCode]
	if !exists {
		// FileCode 不存在於允許類型列表中
		return false, model.MsgFileCodeNotExist, nil, nil
	}

	// 轉換為小寫進行比較
	fileTypeLower := strings.ToLower(fileType)
	for _, allowedType := range types {
		if fileTypeLower == allowedType {
			return true, i18n.Message{}, nil, nil
		}
	}

	templateData := map[string]string{
		"FileType":     fileType,
		"AllowedTypes": strings.Join(types, ", "),
	}
	return false, model.MsgFileTypeNotAllowed, templateData, nil
}

type DocumentFileUploadReq struct {
	UserId   uint64                `form:"-" swaggerignore:"true"`
	FileCode string                `form:"fileCode" binding:"required"`
	File     *multipart.FileHeader `form:"-" swaggerignore:"true"`
}

type DocumentFileUploadResp struct {
	DocumentFileId uint64 `json:"documentFileId"`
}

func (s *documentFileService) Upload(db *gorm.DB, req DocumentFileUploadReq) (DocumentFileUploadResp, error) {
	var resp DocumentFileUploadResp
	var err error
	uuidStr := uuid.NewV4().String()
	uuidName := uuidStr + path.Ext(req.File.Filename)

	reader, err := req.File.Open()
	if err != nil {
		return resp, err
	}
	defer func(reader multipart.File) {
		_ = reader.Close()
	}(reader)

	// 生成文件Model
	documentFile := model.DocumentFile{
		FacilityId:     0,
		UserId:         req.UserId,
		FileCode:       req.FileCode,
		Mode:           xs3.PrivateMode,
		Bucket:         xconfig.OSSConf.Bucket,
		Path:           fmt.Sprintf(OSSDocumentFilePath, req.UserId, req.FileCode, uuidName),
		Uuid:           uuidStr,
		OriginFileName: req.File.Filename,
		FileName:       uuidName,
		FileType:       path.Ext(req.File.Filename),
		FileSize:       uint32(req.File.Size),
	}
	if err = db.Create(&documentFile).Error; err != nil {
		return resp, err
	}

	// 上傳源文件
	err = xs3.UploadObjectFromReader(documentFile.Bucket, documentFile.Path, documentFile.OriginFileName, reader)
	if err != nil {
		return resp, err
	}

	resp.DocumentFileId = documentFile.Id
	return resp, nil
}

type DocumentFileGetPreviewReq struct {
	UserId         uint64 `form:"-" swaggerignore:"true"`
	DocumentFileId uint64 `form:"documentFileId" binding:"required"`
}

type DocumentFileGetPreviewResp struct {
	FileBytes []byte
	Filename  string
}

func (s *documentFileService) Preview(db *gorm.DB, req DocumentFileGetPreviewReq) (DocumentFileGetPreviewResp, error) {
	var err error
	var resp DocumentFileGetPreviewResp
	var m model.DocumentFile
	if err = db.Where("user_id = ?", req.UserId).First(&m, req.DocumentFileId).Error; err != nil {
		return resp, err
	}

	object, err := xs3.GetObject(m.Bucket, m.Path)
	if err != nil {
		return DocumentFileGetPreviewResp{}, err
	}
	resp.FileBytes = object
	resp.Filename = m.OriginFileName
	return resp, nil
}

func (s *documentFileService) GetFileMimeType(path string) string {
	ext := filepath.Ext(path)
	mimeType := mime.TypeByExtension(ext)
	if mimeType == "" {
		mimeType = "application/octet-stream"
	}
	return mimeType
}
