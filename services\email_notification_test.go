package services

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xhermes"
	"github.com/Norray/xrocket/xi18n"
	"github.com/matcornic/hermes/v2"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/stretchr/testify/assert"
)

// 測試設定
func setupEmailTestMocks() {
	xconfig.Setup("../config/app.ini")
	xi18n.Setup([]string{
		"../resource/i18n/active.en.toml",
		"../resource/i18n/active.zh-CN.toml",
		"../resource/i18n/active.zh-HK.toml",
	})
	// 創建測試輸出目錄
	os.MkdirAll("test_output", 0755)
}

// 生成HTML檔案的輔助函數
func generateHTMLFile(t *testing.T, filename string, body hermes.Body) {
	content, err := xhermes.GenerateHTML(HermesDefaultProduct("https://test.medicrew.com/logo.png"), body)
	assert.NoError(t, err)

	filepath := filepath.Join("test_output", filename)
	err = os.WriteFile(filepath, []byte(content), 0644)
	assert.NoError(t, err)

	t.Logf("Generated HTML file: %s", filepath)
}

// 測試1: 新機構申請通知 (admin)
func TestEmailAdminNewFacilityApplication(t *testing.T) {
	setupEmailTestMocks()

	req := NewFacilityApplicationReq{
		FacilityId:        12345,
		FacilityProfileId: 67890,
		Lang:              "en",
		AdminEmail:        []string{"<EMAIL>", "<EMAIL>"},
	}

	taskUrl := "https://test.medicrew.com/admin/facility/67890/12345/approval?_t=**********"

	emailGreeting := i18n.Message{
		ID:    "email.new_facility_application.greeting",
		Other: "Hi",
	}
	emailSignature := i18n.Message{
		ID:    "email.new_facility_application.signature",
		Other: "Thank you",
	}
	emailContent := i18n.Message{
		ID:    "email.new_facility_application.content",
		Other: "This is an automated notification to inform you that a new facility has submitted an application to join the Medic Crew platform.\n\nPlease log in to your admin account to review the application and verify the submitted details.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.new_facility_application.button",
		Other: "Click here to Go To Task",
	}

	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLang(req.Lang, &emailGreeting),
		Name:      "",
		Signature: xi18n.LocalizeWithLang(req.Lang, &emailSignature),
		Intros: []string{
			xi18n.LocalizeWithLang(req.Lang, &emailContent),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLang(req.Lang, &emailButtonText),
					Link:  taskUrl,
				},
			},
		},
	}

	generateHTMLFile(t, "email_admin_new_facility_application_en.html", body)
	t.Log("Generated HTML file for admin new facility application notification")
}

// 測試2: 協議準備完成通知 (facility)
func TestEmailFacilityAgreementReady(t *testing.T) {
	setupEmailTestMocks()

	req := FacilityAgreementReadyReq{
		Lang:         "en",
		FacilityName: "Test Medical Center",
		Email:        "<EMAIL>",
	}

	i18nMap := map[string]string{
		"FacilityName": req.FacilityName,
	}

	emailGreeting := i18n.Message{
		ID:    "email.agreement_ready.greeting",
		Other: "Hi {{.FacilityName}}",
	}
	emailSignature := i18n.Message{
		ID:    "email.agreement_ready.signature",
		Other: "Thank you",
	}
	emailContent := i18n.Message{
		ID:    "email.agreement_ready.content",
		Other: "We have reviewed the information provided by your facility and prepared a draft agreement accordingly.\n\nTo proceed, please log in to Medic Crew to complete your facility details and sign the agreement. You can use the same login credentials (email and password) created during your initial registration.\n\nIf you have any questions or need assistance, please don't hesitate to contact us.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.agreement_ready.button",
		Other: "Click here to Review Agreement",
	}

	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailGreeting, i18nMap),
		Name:      "",
		Signature: xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailContent, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailButtonText, i18nMap),
					Link:  "https://test.medicrew.com/facility/basic",
				},
			},
		},
	}

	generateHTMLFile(t, "email_facility_agreement_ready_en.html", body)
	t.Log("Generated HTML file for facility agreement ready notification")
}

// 測試3: 協議續期提醒 (admin)
func TestEmailFacilityAgreementRenewalReminder(t *testing.T) {
	setupEmailTestMocks()

	req := FacilityAgreementRenewalReminderReq{
		Lang:         "en",
		FacilityName: "Test Medical Center",
		AdminEmail:   []string{"<EMAIL>"},
	}

	i18nMap := map[string]string{
		"FacilityName": req.FacilityName,
	}

	emailGreeting := i18n.Message{
		ID:    "email.agreement_renewal_reminder.greeting",
		Other: "Hi {{.FacilityName}}",
	}
	emailSignature := i18n.Message{
		ID:    "email.agreement_renewal_reminder.signature",
		Other: "Thank you",
	}
	emailContent := i18n.Message{
		ID:    "email.agreement_renewal_reminder.content",
		Other: "This is a friendly reminder that the agreement between your facility and Medic Crew is set to expire in one month. To ensure continued service without interruption, please log in to your account and finalise a new agreement at your earliest convenience.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.agreement_renewal_reminder.button",
		Other: "Click here to Renew Agreement",
	}

	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailGreeting, i18nMap),
		Name:      "",
		Signature: xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailContent, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailButtonText, i18nMap),
					Link:  "https://test.medicrew.com/facility/agreement",
				},
			},
		},
	}

	generateHTMLFile(t, "email_facility_agreement_renewal_reminder_en.html", body)
	t.Log("Generated HTML file for facility agreement renewal reminder notification")
}

// 測試4: 協議過期通知 (facility)
func TestEmailFacilityAgreementExpired(t *testing.T) {
	setupEmailTestMocks()

	req := FacilityAgreementExpiredReq{
		Lang:         "en",
		FacilityName: "Test Medical Center",
		Email:        "<EMAIL>",
	}

	i18nMap := map[string]string{
		"FacilityName": req.FacilityName,
	}

	emailGreeting := i18n.Message{
		ID:    "email.agreement_expired.greeting",
		Other: "Hi {{.FacilityName}}",
	}
	emailSignature := i18n.Message{
		ID:    "email.agreement_expired.signature",
		Other: "Thank you",
	}
	emailContent := i18n.Message{
		ID:    "email.agreement_expired.content",
		Other: "This is to inform you that your agreement with Medic Crew has expired. To continue posting jobs and using our platform, please log in to your account to sign a new agreement.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.agreement_expired.button",
		Other: "Click here to Renew Agreement",
	}

	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailGreeting, i18nMap),
		Name:      "",
		Signature: xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailContent, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailButtonText, i18nMap),
					Link:  "https://test.medicrew.com/facility/basic",
				},
			},
		},
	}

	generateHTMLFile(t, "email_facility_agreement_expired_en.html", body)
	t.Log("Generated HTML file for facility agreement expired notification")
}

// 測試5: 機構資料審核通過 (facility)
func TestEmailFacilityApproved(t *testing.T) {
	setupEmailTestMocks()

	req := FacilityApprovedReq{
		Lang:         "en",
		FacilityName: "Test Medical Center",
		Email:        "<EMAIL>",
	}

	i18nMap := map[string]string{
		"FacilityName": req.FacilityName,
	}

	emailGreeting := i18n.Message{
		ID:    "email.facility_approved.greeting",
		Other: "Hi {{.FacilityName}}",
	}
	emailSignature := i18n.Message{
		ID:    "email.facility_approved.signature",
		Other: "Thank you",
	}
	emailContent := i18n.Message{
		ID:    "email.facility_approved.content",
		Other: "We are pleased to inform you that we have reviewed and approved the detailed information you provided for your facility. You can now log in to your account and start posting jobs to find the right professionals for your facility.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.facility_approved.button",
		Other: "Click here to Login",
	}

	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailGreeting, i18nMap),
		Name:      "",
		Signature: xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailContent, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailButtonText, i18nMap),
					Link:  "https://test.medicrew.com/login",
				},
			},
		},
	}

	generateHTMLFile(t, "email_facility_approved_en.html", body)
	t.Log("Generated HTML file for facility approved notification")
}

// 測試6: 機構資料審核拒絕 (facility)
func TestEmailFacilityRejected(t *testing.T) {
	setupEmailTestMocks()

	req := FacilityRejectedReq{
		Lang:         "en",
		FacilityName: "Test Medical Center",
		Email:        "<EMAIL>",
	}

	i18nMap := map[string]string{
		"FacilityName": req.FacilityName,
	}

	emailGreeting := i18n.Message{
		ID:    "email.facility_rejected.greeting",
		Other: "Hi {{.FacilityName}}",
	}
	emailSignature := i18n.Message{
		ID:    "email.facility_rejected.signature",
		Other: "Thank you",
	}
	emailContent := i18n.Message{
		ID:    "email.facility_rejected.content",
		Other: "We have reviewed the information you submitted for your facility, but we were unable to approve your account at this time. This may be due to incomplete or incorrect details. Please log in to your account to review and resubmit your information.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.facility_rejected.button",
		Other: "Click here to Review Your Application",
	}

	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailGreeting, i18nMap),
		Name:      "",
		Signature: xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailContent, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailButtonText, i18nMap),
					Link:  "https://test.medicrew.com/facility/basic",
				},
			},
		},
	}

	generateHTMLFile(t, "email_facility_rejected_en.html", body)
	t.Log("Generated HTML file for facility rejected notification")
}

// 測試7: 專業人員資料審核通過 (professional)
func TestEmailProfessionalApproved(t *testing.T) {
	setupEmailTestMocks()

	req := ProfessionalApprovedReq{
		Lang:             "en",
		ProfessionalName: "Dr. John Smith",
		Email:            "<EMAIL>",
	}

	i18nMap := map[string]string{
		"ProfessionalName": req.ProfessionalName,
	}

	emailGreeting := i18n.Message{
		ID:    "email.professional_approved.greeting",
		Other: "Hi {{.ProfessionalName}}",
	}
	emailSignature := i18n.Message{
		ID:    "email.professional_approved.signature",
		Other: "Thank you",
	}
	emailContent := i18n.Message{
		ID:    "email.professional_approved.content",
		Other: "We are pleased to inform you that we have reviewed and approved your professional profile. You can now log in to your account and start applying for jobs that match your qualifications and preferences.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.professional_approved.button",
		Other: "Click here to Login",
	}

	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailGreeting, i18nMap),
		Name:      "",
		Signature: xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailContent, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailButtonText, i18nMap),
					Link:  "https://test.medicrew.com/login",
				},
			},
		},
	}

	generateHTMLFile(t, "email_professional_approved_en.html", body)
	t.Log("Generated HTML file for professional approved notification")
}

// 測試8: 專業人員資料審核拒絕 (professional)
func TestEmailProfessionalRejected(t *testing.T) {
	setupEmailTestMocks()

	req := ProfessionalRejectedReq{
		Lang:             "en",
		ProfessionalName: "Dr. John Smith",
		Email:            "<EMAIL>",
	}

	i18nMap := map[string]string{
		"ProfessionalName": req.ProfessionalName,
	}

	emailGreeting := i18n.Message{
		ID:    "email.professional_rejected.greeting",
		Other: "Hi {{.ProfessionalName}}",
	}
	emailSignature := i18n.Message{
		ID:    "email.professional_rejected.signature",
		Other: "Thank you",
	}
	emailContent := i18n.Message{
		ID:    "email.professional_rejected.content",
		Other: "We have reviewed your professional profile, but we were unable to approve your account at this time. This may be due to incomplete or incorrect details. Please log in to your account to review and resubmit your information.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.professional_rejected.button",
		Other: "Click here to Review Your Profile",
	}

	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailGreeting, i18nMap),
		Name:      "",
		Signature: xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailContent, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailButtonText, i18nMap),
					Link:  "https://test.medicrew.com/professional/basic",
				},
			},
		},
	}

	generateHTMLFile(t, "email_professional_rejected_en.html", body)
	t.Log("Generated HTML file for professional rejected notification")
}

// 測試9: 發票通知 (facility)
func TestEmailFacilityInvoiceNotification(t *testing.T) {
	setupEmailTestMocks()

	req := FacilityInvoiceNotificationReq{
		Lang:         "en",
		FacilityName: "Test Medical Center",
		Email:        "<EMAIL>",
		DocumentId:   12345,
	}

	i18nMap := map[string]string{
		"FacilityName": req.FacilityName,
	}

	emailGreeting := i18n.Message{
		ID:    "email.invoice_notification.greeting",
		Other: "Hi {{.FacilityName}}",
	}
	emailSignature := i18n.Message{
		ID:    "email.invoice_notification.signature",
		Other: "Thank you",
	}
	emailContent := i18n.Message{
		ID:    "email.invoice_notification.content",
		Other: "Your invoice is now ready for download. Please log in to your account to view and download your invoice.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.invoice_notification.button",
		Other: "Click here to View Invoice",
	}

	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailGreeting, i18nMap),
		Name:      "",
		Signature: xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailContent, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailButtonText, i18nMap),
					Link:  "https://test.medicrew.com/facility/invoice",
				},
			},
		},
	}

	generateHTMLFile(t, "email_facility_invoice_notification_en.html", body)
	t.Log("Generated HTML file for facility invoice notification")
}

// 測試運行所有電郵函數的總測試
func TestAllEmailNotifications(t *testing.T) {
	setupEmailTestMocks()

	t.Run("AdminNewFacilityApplication", TestEmailAdminNewFacilityApplication)
	t.Run("FacilityAgreementReady", TestEmailFacilityAgreementReady)
	t.Run("FacilityAgreementRenewalReminder", TestEmailFacilityAgreementRenewalReminder)
	t.Run("FacilityAgreementExpired", TestEmailFacilityAgreementExpired)
	t.Run("FacilityApproved", TestEmailFacilityApproved)
	t.Run("FacilityRejected", TestEmailFacilityRejected)
	t.Run("ProfessionalApproved", TestEmailProfessionalApproved)
	t.Run("ProfessionalRejected", TestEmailProfessionalRejected)
	t.Run("FacilityInvoiceNotification", TestEmailFacilityInvoiceNotification)

	t.Log("Email notification tests completed. Check test_output/ directory for generated HTML files.")
	t.Log("Generated files:")
	t.Log("- email_admin_new_facility_application_en.html")
	t.Log("- email_facility_agreement_ready_en.html")
	t.Log("- email_facility_agreement_renewal_reminder_en.html")
	t.Log("- email_facility_agreement_expired_en.html")
	t.Log("- email_facility_approved_en.html")
	t.Log("- email_facility_rejected_en.html")
	t.Log("- email_professional_approved_en.html")
	t.Log("- email_professional_rejected_en.html")
	t.Log("- email_facility_invoice_notification_en.html")
}
