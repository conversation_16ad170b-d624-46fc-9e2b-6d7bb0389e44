package services

import (
	"fmt"
	"io"
	"mime"
	"mime/multipart"
	"path"
	"path/filepath"
	"strings"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xs3"
	"github.com/Norray/xrocket/xtool"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

var FacilityFileService = new(facilityFileService)

type facilityFileService struct{}

var FacilityAiFileMap = map[string]bool{
	model.FacilityFileCodePublicLiabilityInsurance: true,
}

func (s *facilityFileService) CheckIdExist(db *gorm.DB, m *model.FacilityFile, id uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_file.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	var err error
	if err = db.First(&m, id).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

func (s *facilityFileService) CheckIdsExist(db *gorm.DB, facilityId uint64, ids []uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_file.ids.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	if len(ids) == 0 {
		return true, i18n.Message{}, nil
	}
	ids = xtool.Uint64ArrayDeduplication(ids)
	var count int64
	var err error
	if err = db.Model(&model.FacilityFile{}).
		Where("facility_id = ?", facilityId).
		Where("id IN (?)", ids).Count(&count).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if count != int64(len(ids)) {
		return false, msg, nil
	}
	return true, msg, nil
}

func (s *facilityFileService) CheckFileCodeExist(fileCode string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_file.code.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	for _, code := range model.FacilityFileCodes {
		if code == fileCode {
			return true, i18n.Message{}, nil
		}
	}
	return false, msg, nil
}

// 檢查文件大小是否符合限制
func (s *facilityFileService) CheckFileSize(fileCode string, fileSize int64) (bool, i18n.Message, map[string]string, error) {
	limitations := model.FacilityFile{}.FileSizeLimitations()
	maxSize, exists := limitations[fileCode]
	if !exists {
		// FileCode 不存在於限制列表中
		return false, model.MsgFileCodeNotExist, nil, nil
	}

	if fileSize > maxSize {
		templateData := map[string]string{
			"MaxSize": fmt.Sprintf("%d", maxSize/(1024*1024)),
		}
		return false, model.MsgFileSizeTooLarge, templateData, nil
	}
	return true, i18n.Message{}, nil, nil
}

// 檢查文件類型是否允許
func (s *facilityFileService) CheckFileType(fileCode string, fileType string) (bool, i18n.Message, map[string]string, error) {
	allowedTypes := model.FacilityFile{}.AllowedFileTypes()
	types, exists := allowedTypes[fileCode]
	if !exists {
		// FileCode 不存在於允許類型列表中
		return false, model.MsgFileCodeNotExist, nil, nil
	}

	// 轉換為小寫進行比較
	fileTypeLower := strings.ToLower(fileType)
	for _, allowedType := range types {
		if fileTypeLower == allowedType {
			return true, i18n.Message{}, nil, nil
		}
	}

	templateData := map[string]string{
		"FileType":     fileType,
		"AllowedTypes": strings.Join(types, ", "),
	}
	return false, model.MsgFileTypeNotAllowed, templateData, nil
}

// 檢查機構檔案是否已上傳對應職位的培訓文件
func (s *facilityFileService) CheckOrientationDocumentationExist(db *gorm.DB, facilityProfileIds []uint64, positionProfession string) (bool, i18n.Message, error) {
	// 根據職位獲取對應的培訓文件代碼
	orientationCode := model.GetOrientationDocumentationCodeByProfession(positionProfession)
	if orientationCode == "" {
		// 如果職位不需要培訓文件，則通過檢查
		return true, i18n.Message{}, nil
	}

	// 去重
	facilityProfileIds = xtool.Uint64ArrayDeduplication(facilityProfileIds)

	// 檢查是否存在對應的培訓文件關聯
	var count int64
	err := db.Table("facility_file_relation ffr").
		Joins("INNER JOIN facility_file ff ON ffr.facility_file_id = ff.id").
		Where("ffr.facility_profile_id IN (?)", facilityProfileIds).
		Where("ff.file_code = ?", orientationCode).
		Count(&count).Error

	if err != nil {
		return false, i18n.Message{}, err
	}

	if count != int64(len(facilityProfileIds)) {
		return false, i18n.Message{
			ID:    "checker.facility_file.orientation_documentation.not_found",
			Other: "Some thing went wrong, please try again later.",
		}, nil
	}

	return true, i18n.Message{}, nil
}

func (s *facilityFileService) CheckFileExist(db *gorm.DB, files *[]model.FacilityFile, facilityId uint64, fileCodes []string, ids []uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_file.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	if len(ids) == 0 {
		return true, i18n.Message{}, nil
	}
	ids = xtool.Uint64ArrayDeduplication(ids)
	if err := db.Where("facility_id = ?", facilityId).
		Where("file_code IN (?)", fileCodes).
		Where("id IN (?)", ids).Find(files).Error; err != nil {
		return false, msg, err
	}
	if len(ids) == len(*files) {
		return true, i18n.Message{}, nil
	}
	return false, msg, nil
}

// 檢查專業人士是否可以訪問機構的文件 - 這裡只檢查工作文件
func (s *facilityFileService) ProfessionalCheckJobFileExist(db *gorm.DB, facilityFileId uint64, reqUserId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.facility_file.id.professional.does_not_exist",
		Other: "No such file, please try again later.",
	}

	// 一次查詢檢查所有條件：
	// 1. 機構文件存在
	// 2. 專業人士存在
	// 3. 專業人士與該機構有已接受的工作關係
	// 4. 該文件與工作相關
	var count int64
	err := db.Table("facility_file AS ff").
		Joins("JOIN job_file AS jf ON jf.facility_file_id = ff.id AND jf.facility_id = ff.facility_id").
		Joins("JOIN job_application AS ja ON ja.job_id = jf.job_id").
		Joins("JOIN professional AS p ON p.id = ja.professional_id AND p.user_id = ja.user_id").
		Where("ff.id = ?", facilityFileId).
		Where("ja.user_id = ?", reqUserId).
		Where("ja.status = ?", model.JobApplicationStatusAccept).
		Where("ja.accept = ?", model.JobApplicationAcceptY).
		Where("ja.deleted != ?", model.JobApplicationDeletedY).
		Count(&count).Error

	if xgorm.IsSqlErr(err) {
		return false, msg, err
	}

	// 如果 count > 0，表示專業人士有權限訪問該文件
	if count > 0 {
		return true, i18n.Message{}, nil
	}

	return false, msg, nil
}

type FacilityFileUploadReq struct {
	FacilityId      uint64                `form:"facilityId" binding:"required"`
	FileCode        string                `form:"fileCode" binding:"required"`
	File            *multipart.FileHeader `form:"-" swaggerignore:"true"`
	AiDateAndNumber AiDateAndNumber       `form:"-" swaggerignore:"true"`
}

type FacilityFileUploadResp struct {
	FacilityFileId uint64 `json:"facilityFileId"`
	ExpireDate     string `json:"expireDate"` // 到期日
	IssueDate      string `json:"issueDate"`  // 發行日期
	Number         string `json:"number"`     // 識別號碼
}

func (s *facilityFileService) Upload(db *gorm.DB, req FacilityFileUploadReq) (FacilityFileUploadResp, error) {
	var resp FacilityFileUploadResp
	var err error
	uuidStr := uuid.NewV4().String()
	uuidName := uuidStr + path.Ext(req.File.Filename)

	reader, err := req.File.Open()
	if err != nil {
		return resp, err
	}
	defer func(reader multipart.File) {
		_ = reader.Close()
	}(reader) // Ensure the reader is closed

	// 構建縮略圖
	thumbnailResp, err := ImageService.ConvertToThumbnail(reader, req.File.Filename, uuidStr)
	if err != nil {
		return resp, err
	}

	// 生成文件Model
	facilityFile := model.FacilityFile{
		FacilityId:        req.FacilityId,
		FileCode:          req.FileCode,
		Mode:              xs3.PrivateMode,
		Bucket:            xconfig.OSSConf.Bucket,
		Path:              fmt.Sprintf(OSSFacilityFilePath, req.FacilityId, req.FileCode, uuidName),
		Uuid:              uuidStr,
		OriginFileName:    req.File.Filename,
		FileName:          uuidName,
		FileType:          path.Ext(req.File.Filename),
		FileSize:          uint32(req.File.Size),
		ThumbnailPath:     fmt.Sprintf(OSSFacilityFilePath, req.FacilityId, req.FileCode, thumbnailResp.ThumbnailUuidName),
		ThumbnailFileSize: thumbnailResp.Size,
	}
	if err = db.Create(&facilityFile).Error; err != nil {
		return resp, err
	}
	// 先上傳縮略圖
	err = xs3.UploadObjectFromReader(facilityFile.Bucket, facilityFile.ThumbnailPath, facilityFile.OriginFileName, thumbnailResp.ThumbnailBody)
	if err != nil {
		return resp, err
	}
	// 重置 reader 指針
	_, err = reader.Seek(0, io.SeekStart)
	if err != nil {
		return resp, err
	}
	// 最後再上傳源文件
	err = xs3.UploadObjectFromReader(facilityFile.Bucket, facilityFile.Path, facilityFile.OriginFileName, reader)
	if err != nil {
		// 源文件上傳失敗，刪除縮略圖
		_ = xs3.DeleteObject(facilityFile.Bucket, facilityFile.ThumbnailPath)
		return resp, err
	}
	resp.FacilityFileId = facilityFile.Id
	resp.ExpireDate = req.AiDateAndNumber.ExpireDate
	resp.IssueDate = req.AiDateAndNumber.IssueDate
	resp.Number = req.AiDateAndNumber.Number
	return resp, nil
}

type FacilityFileGetPreviewReq struct {
	FacilityFileId uint64 `form:"facilityFileId" binding:"required"`
	Thumb          string `form:"thumb" binding:"required,oneof=Y N"`
}

type FacilityFileGetPreviewResp struct {
	FileBytes    []byte
	Filename     string
	UuidFileName string
}

func (s *facilityFileService) Preview(db *gorm.DB, req FacilityFileGetPreviewReq) (FacilityFileGetPreviewResp, error) {
	var err error
	var resp FacilityFileGetPreviewResp
	var m model.FacilityFile
	if err = db.First(&m, req.FacilityFileId).Error; err != nil {
		return resp, err
	}
	var object []byte
	if req.Thumb == "Y" {
		object, err = xs3.GetObject(m.Bucket, m.ThumbnailPath)
	} else {
		object, err = xs3.GetObject(m.Bucket, m.Path)
	}
	if err != nil {
		return FacilityFileGetPreviewResp{}, err
	}
	resp.FileBytes = object
	resp.Filename = m.OriginFileName
	resp.UuidFileName = m.FileName
	return resp, nil
}

func (s *facilityFileService) GetFileMimeType(path string) string {
	ext := filepath.Ext(path)
	mimeType := mime.TypeByExtension(ext)
	if mimeType == "" {
		mimeType = "application/octet-stream"
	}
	return mimeType
}
