package services

import (
	"github.com/Norray/medic-crew/model"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gorm.io/gorm"
)

var FacilitySpecialisationService = new(facilitySpecialisationService)

type facilitySpecialisationService struct{}

// 查詢機構專業細分請求
type FacilitySpecialisationInquireReq struct {
	FacilityId   uint64 `form:"facilityId" binding:"required"`              // 機構Id
	SelectedOnly string `form:"selectedOnly" binding:"omitempty,oneof=Y N"` // 是否只返回已選中的專業，Y=只返回已選中，N=返回所有專業
}

// 機構專業細分樹節點（擴展 SelectionListResp 以包含選擇狀態）
type FacilitySpecialisationTreeNode struct {
	SelectionType string                            `json:"selection_type"`     // 選項類型
	Code          string                            `json:"code"`               // 編號
	Name          string                            `json:"name"`               // 名稱
	Selected      bool                              `json:"selected"`           // 是否已選擇
	Children      *[]FacilitySpecialisationTreeNode `json:"children,omitempty"` // 子選項
}

// 更新機構專業細分請求
type FacilitySpecialisationEditReq struct {
	FacilityId               uint64   `json:"facilityId" binding:"required"`                // 機構Id
	MedicalPractitionerCodes []string `json:"medicalPractitionerCodes" binding:"omitempty"` // 醫療從業員專業代碼
	EnrolledNurseCodes       []string `json:"enrolledNurseCodes" binding:"omitempty"`       // 登記護士專業代碼
	RegisteredNurseCodes     []string `json:"registeredNurseCodes" binding:"omitempty"`     // 註冊護士專業代碼
	PersonalCareWorkerCodes  []string `json:"personalCareWorkerCodes" binding:"omitempty"`  // 個人護理員專業代碼
}

// 獲取所有支持的職位列表
func (s *facilitySpecialisationService) getSupportedPositions() []string {
	return []string{
		model.JobPositionProfessionMedicalPractitioner,
		model.JobPositionProfessionEnrolledNurse,
		model.JobPositionProfessionRegisteredNurse,
		model.JobPositionProfessionPersonalCareWorker,
	}
}

// 獲取機構專業細分樹狀結構（按職位分組）
func (s *facilitySpecialisationService) GetSpecialitiesTree(db *gorm.DB, facilityId uint64, selectedOnly string) (map[string][]FacilitySpecialisationTreeNode, error) {
	positions := s.getSupportedPositions()
	result := make(map[string][]FacilitySpecialisationTreeNode)

	for _, position := range positions {
		// 初始化為空數組，確保不返回 null
		result[position] = []FacilitySpecialisationTreeNode{}

		// 獲取該職位的已選專業代碼
		selectedCodes := s.getSelectedCodesByPosition(db, facilityId, position)

		// 根據職位獲取對應的選項類型
		selectionType := s.getSelectionTypeByPosition(position)
		if selectionType == "" {
			continue
		}

		// 獲取該職位的專業樹
		tree, err := s.getPositionSpecialitiesTree(db, selectionType, selectedCodes, selectedOnly)
		if err != nil {
			return nil, err
		}
		if tree == nil {
			tree = []FacilitySpecialisationTreeNode{}
		}

		result[position] = tree
	}

	return result, nil
}

// 獲取指定職位的已選專業代碼
func (s *facilitySpecialisationService) getSelectedCodesByPosition(db *gorm.DB, facilityId uint64, position string) []string {
	var selectedCodes []string
	if facilityId > 0 {
		db.Model(&model.FacilitySpecialisation{}).
			Where("facility_id = ? AND position = ?", facilityId, position).
			Pluck("code", &selectedCodes)
	}
	return selectedCodes
}

// 根據職位獲取對應的選項類型
func (s *facilitySpecialisationService) getSelectionTypeByPosition(position string) string {
	switch position {
	case model.JobPositionProfessionMedicalPractitioner:
		return model.SelectionTypePreferredSpecialityMedicalPractitioner
	case model.JobPositionProfessionEnrolledNurse, model.JobPositionProfessionRegisteredNurse:
		return model.SelectionTypePreferredSpecialityNurse
	case model.JobPositionProfessionPersonalCareWorker:
		return model.SelectionTypePreferredSpecialityPersonalCareWorker
	default:
		return ""
	}
}

// 獲取指定職位的專業細分樹
func (s *facilitySpecialisationService) getPositionSpecialitiesTree(db *gorm.DB, selectionType string, selectedCodes []string, selectedOnly string) ([]FacilitySpecialisationTreeNode, error) {
	// 獲取專業細分樹狀結構
	selectionTree, err := SelectionService.List(db, SelectionListReq{
		SelectionTypes: selectionType,
	})
	if err != nil {
		return nil, err
	}

	// 創建已選擇代碼的映射
	selectedCodesMap := make(map[string]bool)
	for _, code := range selectedCodes {
		selectedCodesMap[code] = true
	}

	// 轉換為包含選擇狀態的樹狀結構
	treeNodes := s.convertToFacilitySpecialisationTree(selectionTree, selectedCodesMap, selectedOnly)

	// 返回第一個選項類型的樹節點
	for _, nodes := range treeNodes {
		return nodes, nil
	}

	return []FacilitySpecialisationTreeNode{}, nil
}

func (s *facilitySpecialisationService) convertToFacilitySpecialisationTree(selectionTree map[string][]SelectionListResp, selectedCodes map[string]bool, selectedOnly string) map[string][]FacilitySpecialisationTreeNode {
	result := make(map[string][]FacilitySpecialisationTreeNode)

	for selectionType, nodes := range selectionTree {
		var convertedNodes []FacilitySpecialisationTreeNode
		for _, node := range nodes {
			isSelected := selectedCodes[node.Code]

			// 處理子節點
			var children []FacilitySpecialisationTreeNode
			hasSelectedChildren := false
			allChildrenSelected := true
			totalChildren := 0
			selectedChildrenCount := 0

			if node.Children != nil {
				for _, child := range *node.Children {
					childSelected := selectedCodes[child.Code]
					totalChildren++

					// 如果只返回已選中的專業，且子節點未選中，則跳過
					if selectedOnly == "Y" && !childSelected {
						continue
					}

					if childSelected {
						hasSelectedChildren = true
						selectedChildrenCount++
					} else {
						allChildrenSelected = false
					}

					children = append(children, FacilitySpecialisationTreeNode{
						SelectionType: child.SelectionType,
						Code:          child.Code,
						Name:          child.Name,
						Selected:      childSelected,
					})
				}

				// 如果沒有子節點，則 allChildrenSelected 應該為 false
				if totalChildren == 0 {
					allChildrenSelected = false
				}
			} else {
				allChildrenSelected = false
			}

			// 如果只返回已選中的專業，檢查當前節點或其子節點是否有被選中
			if selectedOnly == "Y" && !isSelected && !hasSelectedChildren {
				continue
			}

			// 父級的 Selected 邏輯：
			// 1. 如果父級本身被選中，則為 true
			// 2. 如果父級有子節點，則必須所有子節點都被選中才為 true
			// 3. 如果父級沒有子節點，則只看父級本身是否被選中
			parentSelected := isSelected
			if totalChildren > 0 {
				parentSelected = isSelected || allChildrenSelected
			}

			convertedNode := FacilitySpecialisationTreeNode{
				SelectionType: node.SelectionType,
				Code:          node.Code,
				Name:          node.Name,
				Selected:      parentSelected,
			}

			if len(children) > 0 {
				convertedNode.Children = &children
			}

			convertedNodes = append(convertedNodes, convertedNode)
		}
		result[selectionType] = convertedNodes
	}

	return result
}

// 更新機構專業細分（按職位）
func (s *facilitySpecialisationService) UpdateSpecialisations(db *gorm.DB, req FacilitySpecialisationEditReq) error {
	// 刪除現有記錄
	if err := db.Where("facility_id = ?", req.FacilityId).Delete(&model.FacilitySpecialisation{}).Error; err != nil {
		return err
	}

	// 準備新記錄
	positionCodes := map[string][]string{
		model.JobPositionProfessionMedicalPractitioner: req.MedicalPractitionerCodes,
		model.JobPositionProfessionEnrolledNurse:       req.EnrolledNurseCodes,
		model.JobPositionProfessionRegisteredNurse:     req.RegisteredNurseCodes,
		model.JobPositionProfessionPersonalCareWorker:  req.PersonalCareWorkerCodes,
	}

	// 生成 model 數組
	var specialisations []model.FacilitySpecialisation
	for position, codes := range positionCodes {
		for _, code := range codes {
			if code != "" { // 跳過空字符串
				specialisation := model.FacilitySpecialisation{
					FacilityId: req.FacilityId,
					Position:   position,
					Code:       code,
				}
				specialisations = append(specialisations, specialisation)
			}
		}
	}

	// 批量創建記錄
	if len(specialisations) > 0 {
		if err := db.CreateInBatches(&specialisations, 100).Error; err != nil {
			return err
		}
	}

	return nil
}

// 獲取所有相關的選項類型（包括父級和子級）
func (s *facilitySpecialisationService) getAllSelectionTypes(db *gorm.DB, selectionType string) ([]string, error) {
	selectionTree, err := SelectionService.List(db, SelectionListReq{
		SelectionTypes: selectionType,
	})
	if err != nil {
		return nil, err
	}

	// 收集所有可能的 selection_type（包括父級和子級）
	allSelectionTypes := []string{selectionType}
	for _, selections := range selectionTree {
		for _, selection := range selections {
			allSelectionTypes = append(allSelectionTypes, selection.Code)
		}
	}

	return allSelectionTypes, nil
}

// 根據職位檢查專業代碼是否有效
func (s *facilitySpecialisationService) CheckCodesValidByPosition(db *gorm.DB, position string, codes []string) (bool, i18n.Message, error) {
	if len(codes) == 0 {
		return true, i18n.Message{
			ID:    "checker.facility_specialisation.codes.valid",
			Other: "All codes are valid for the specified position.",
		}, nil
	}

	// 根據職位獲取對應的選項類型
	selectionType := s.getSelectionTypeByPosition(position)
	if selectionType == "" {
		return false, i18n.Message{
			ID:    "checker.facility_specialisation.position.invalid",
			Other: "The specified position is not supported.",
		}, nil
	}

	// 獲取所有相關的選項類型
	allSelectionTypes, err := s.getAllSelectionTypes(db, selectionType)
	if err != nil {
		return false, i18n.Message{}, err
	}

	// 檢查代碼是否在任何相關的選項類型中存在
	var count int64
	err = db.Model(&model.Selection{}).
		Where("code IN (?) AND selection_type IN (?)", codes, allSelectionTypes).
		Count(&count).Error
	if err != nil {
		return false, i18n.Message{}, err
	}

	if int(count) != len(codes) {
		return false, i18n.Message{
			ID:    "checker.facility_specialisation.codes.invalid",
			Other: "One or more codes are invalid for the specified position.",
		}, nil
	}

	return true, i18n.Message{
		ID:    "checker.facility_specialisation.codes.valid",
		Other: "All codes are valid for the specified position.",
	}, nil
}

type InquireByPositionReq struct {
	FacilityId uint64 `form:"facilityId" binding:"required"`                                                                               // 機構Id
	Position   string `form:"position" binding:"required,oneof=MEDICAL_PRACTITIONER ENROLLED_NURSE REGISTERED_NURSE PERSONAL_CARE_WORKER"` // 職位 MEDICAL_PRACTITIONER ENROLLED_NURSE REGISTERED_NURSE PERSONAL_CARE_WORKER
}

// 獲取指定機構和職業的專業數據
func (s *facilitySpecialisationService) InquireByPosition(db *gorm.DB, req InquireByPositionReq) ([]FacilitySpecialisationTreeNode, error) {
	// 檢查職位是否支援
	supportedPositions := s.getSupportedPositions()
	isSupported := false
	for _, supportedPosition := range supportedPositions {
		if supportedPosition == req.Position {
			isSupported = true
			break
		}
	}

	if !isSupported {
		return []FacilitySpecialisationTreeNode{}, nil
	}

	// 獲取該職位的已選專業代碼
	selectedCodes := s.getSelectedCodesByPosition(db, req.FacilityId, req.Position)

	// 根據職位獲取對應的選項類型
	selectionType := s.getSelectionTypeByPosition(req.Position)
	if selectionType == "" {
		return []FacilitySpecialisationTreeNode{}, nil
	}

	// 獲取該職位的專業樹（返回所有專業，不限制只返回已選中的）
	tree, err := s.getPositionSpecialitiesTree(db, selectionType, selectedCodes, "N")
	if err != nil {
		return nil, err
	}
	if tree == nil {
		tree = []FacilitySpecialisationTreeNode{}
	}

	return tree, nil
}
