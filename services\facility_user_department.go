package services

import (
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xmodel"
	"gorm.io/gorm"
)

var FacilityUserDepartmentService = new(facilityUserDepartmentService)

type facilityUserDepartmentService struct{}

// FilterByCanAccessDepartment column 應為某個表的 departmentId，這樣將會過濾該userId所能接觸的 department
func (s facilityUserDepartmentService) FilterByCanAccessDepartment(departmentIdColumn string, departments []model.Department) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		departmentIds := make([]uint64, 0)
		for _, department := range departments {
			if department.AccessAllDepartment == "Y" {
				// 無需篩選
				return db
			} else {
				departmentIds = append(departmentIds, department.Id)
			}
		}
		if len(departmentIds) == 0 {
			db = db.Where("1 = 0") // 不返回任何
		}
		db = db.Where(departmentIdColumn+" IN (?)", departmentIds)
		return db
	}
}

// FilterByCanAccessJobApplication column 應為某個表的 jobApplicationId，這樣將會過濾該userId所能接觸的 department
func (s facilityUserDepartmentService) FilterByCanAccessJobApplication(jobApplicationIdColumn string, departments []model.Department) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		departmentIds := make([]uint64, 0)
		var facilityId uint64
		for _, department := range departments {
			facilityId = department.FacilityId
			if department.AccessAllDepartment == "Y" {
				// 無需篩選
				return db
			} else {
				departmentIds = append(departmentIds, department.Id)
			}
		}
		if len(departmentIds) == 0 {
			db = db.Where("1 = 0") // 不返回任何
		}
		db = db.Where(jobApplicationIdColumn+" IN (?)", xgorm.DB.Select("c_ja.id").
			Table("job_application c_ja").
			Joins("JOIN job c_j ON c_j.id = c_ja.job_id ").
			Joins("JOIN job_department AS c_jd ON c_jd.job_id = c_j.id").
			Where("c_jd.department_id IN (?)", departmentIds).
			Where("c_ja.facility_id = ?", facilityId).
			Group("c_ja.id"))

		return db
	}
}

// FilterByCanAccessJob column 應為某個表的 jonId，這樣將會過濾該userId所能接觸的 department
func (s facilityUserDepartmentService) FilterByCanAccessJob(jobIdColumn string, departments []model.Department) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		departmentIds := make([]uint64, 0)
		var facilityId uint64
		for _, department := range departments {
			facilityId = department.FacilityId
			if department.AccessAllDepartment == "Y" {
				// 無需篩選
				return db
			} else {
				departmentIds = append(departmentIds, department.Id)
			}
		}
		if len(departmentIds) == 0 {
			db = db.Where("1 = 0") // 不返回任何
		}
		db = db.Where(jobIdColumn+" IN (?)", xgorm.DB.Select("c_j.id").
			Table("job c_j").
			Joins("JOIN job_department AS c_jd ON c_jd.job_id = c_j.id").
			Where("c_jd.department_id IN (?)", departmentIds).
			Where("c_j.facility_id = ?", facilityId).
			Group("c_j.id"))
		return db
	}
}

// FilterByCanAccessDocument column 應為某個表的 documentId，這樣將會過濾該userId所能接觸的 department
func (s facilityUserDepartmentService) FilterByCanAccessDocument(documentIdColumn string, departments []model.Department) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		departmentIds := make([]uint64, 0)
		var facilityId uint64
		for _, department := range departments {
			facilityId = department.FacilityId
			if department.AccessAllDepartment == "Y" {
				// 無需篩選
				return db
			} else {
				departmentIds = append(departmentIds, department.Id)
			}
		}
		if len(departmentIds) == 0 {
			db = db.Where("1 = 0") // 不返回任何
		}
		db = db.Where(documentIdColumn+" IN (?)", xgorm.DB.Select("c_d.id").
			Table("document c_d").
			Joins("JOIN job_application c_ja ON c_ja.id = c_d.job_application_id").
			Joins("JOIN job c_j ON c_j.id = c_ja.job_id ").
			Joins("JOIN job_department AS c_jd ON c_jd.job_id = c_j.id").
			Where("c_jd.department_id IN (?)", departmentIds).
			Where("c_d.facility_id = ?", facilityId).
			Group("c_d.id"))
		return db
	}
}

func (s facilityUserDepartmentService) GetCanAccessDepartmentByUserId(db *gorm.DB, userId uint64, handler func(department []model.Department)) error {
	var err error
	var user xmodel.User
	if err = db.Where("id = ?", userId).First(&user).Error; err != nil {
		return err
	}
	if user.UserType == model.UserUserTypeFacilityUser {
		// 只有機構才需要篩選
		var departments []model.Department
		if err = db.Select("d.*").Table("facility_user_department fud").
			Joins("JOIN department AS d on d.id = fud.department_id").
			Where("fud.user_id = ?", userId).
			Find(&departments).Error; err != nil {
			return err
		}
		handler(departments)
	}
	return nil
}
