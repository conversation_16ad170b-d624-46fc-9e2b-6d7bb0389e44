package services

import (
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
)

func TestFixJobDepartmentData(t *testing.T) {
	// 修復數據，每間 Facility 增加一個叫 HR 的部門（如果不存在），所有未綁定部門的job，記錄為綁定 HR
	xconfig.Setup("../config/app.ini")
	xgorm.DefaultSetup()
	var allFacilities []model.Facility
	var err error
	if err = xgorm.DB.Find(&allFacilities).Error; err != nil {
		t.Error(err)
	}
	for _, v := range allFacilities {
		var hrDepartment model.Department
		if err = xgorm.DB.Where("name = ?", "HR").Where("facility_id = ?", v.Id).First(&hrDepartment).Error; xgorm.IsSqlErr(err) {
			t.Error(err)
		}
		if xgorm.IsNotFoundErr(err) {
			var serviceLocation model.ServiceLocation
			if err = xgorm.DB.Where("facility_id = ?", v.Id).First(&serviceLocation).Error; err != nil {
				// 連Location都沒有，job也不會有
				continue
			}
			hrDepartment = model.Department{
				Name:                "HR",
				FacilityId:          v.Id,
				ServiceLocationId:   serviceLocation.Id,
				AccessAllDepartment: "Y",
				Status:              model.DepartmentStatusEnable,
			}
			err = xgorm.DB.Create(&hrDepartment).Error
			if err != nil {
				t.Error(err)
			}
		}
		var jobList []model.Job
		if err = xgorm.DB.Where("facility_id = ?", v.Id).Find(&jobList).Error; err != nil {
			t.Error(err)
		}
		for _, j := range jobList {
			var jobDepartment model.JobDepartment
			if err = xgorm.DB.Where("department_id = ?", hrDepartment.Id).Where("job_id = ?", j.Id).First(&jobDepartment).Error; xgorm.IsSqlErr(err) {
				t.Error(err)
			}
			if xgorm.IsNotFoundErr(err) {
				jobDepartment.DepartmentId = hrDepartment.Id
				jobDepartment.JobId = j.Id
				err = xgorm.DB.Create(&jobDepartment).Error
				if err != nil {
					t.Error(err)
				}
			}
		}
	}
}
