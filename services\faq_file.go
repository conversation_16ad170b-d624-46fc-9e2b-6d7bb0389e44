package services

import (
	"fmt"
	"io"
	"mime/multipart"
	"path"
	"strings"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xs3"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

var FaqFileService = new(faqFileService)

type faqFileService struct{}

func (s *faqFileService) CheckUuidExist(db *gorm.DB, m *model.FaqFile, uuid string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.faq_file.uuid.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	var err error
	if err = db.First(&m, "uuid = ?", uuid).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

func (s *faqFileService) CheckUuidsExist(db *gorm.DB, uuids []string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.faq_file.uuid.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	var err error
	var count int64
	uuids = StringArrayDistinct(uuids)
	if err = db.Model(&model.FaqFile{}).Where("uuid IN (?)", uuids).Count(&count).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if count != int64(len(uuids)) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

func (s *faqFileService) CheckFileCodeExist(fileCode string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.faq_file.code.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	for _, code := range model.FaqFileCodes {
		if code == fileCode {
			return true, i18n.Message{}, nil
		}
	}
	return false, msg, nil
}

// 檢查文件大小是否符合限制
func (s *faqFileService) CheckFileSize(fileCode string, fileSize int64) (bool, i18n.Message, map[string]string, error) {
	limitations := model.FaqFile{}.FileSizeLimitations()
	maxSize, exists := limitations[fileCode]
	if !exists {
		// FileCode 不存在於限制列表中
		return false, model.MsgFileCodeNotExist, nil, nil
	}

	if fileSize > maxSize {
		templateData := map[string]string{
			"MaxSize": fmt.Sprintf("%d", maxSize/(1024*1024)),
		}
		return false, model.MsgFileSizeTooLarge, templateData, nil
	}
	return true, i18n.Message{}, nil, nil
}

// 檢查文件類型是否允許
func (s *faqFileService) CheckFileType(fileCode string, fileType string) (bool, i18n.Message, map[string]string, error) {
	allowedTypes := model.FaqFile{}.AllowedFileTypes()
	types, exists := allowedTypes[fileCode]
	if !exists {
		// FileCode 不存在於允許類型列表中
		return false, model.MsgFileCodeNotExist, nil, nil
	}

	// 轉換為小寫進行比較
	fileTypeLower := strings.ToLower(fileType)
	for _, allowedType := range types {
		if fileTypeLower == strings.ToLower(allowedType) {
			return true, i18n.Message{}, nil, nil
		}
	}

	templateData := map[string]string{
		"FileType":     fileType,
		"AllowedTypes": strings.Join(types, ", "),
	}
	return false, model.MsgFileTypeNotAllowed, templateData, nil
}

type FaqFileUploadReq struct {
	FileCode string                `form:"fileCode" binding:"required"`
	File     *multipart.FileHeader `form:"-" swaggerignore:"true"`
}

type FaqFileUploadResp struct {
	FaqFileUuid string `json:"faqFileUuid"`
}

func (s *faqFileService) Upload(db *gorm.DB, req FaqFileUploadReq) (FaqFileUploadResp, error) {
	var resp FaqFileUploadResp
	var err error
	uuidStr := uuid.NewV4().String()
	uuidName := uuidStr + path.Ext(req.File.Filename)

	reader, err := req.File.Open()
	if err != nil {
		return resp, err
	}
	defer func(reader multipart.File) {
		_ = reader.Close()
	}(reader) // Ensure the reader is closed

	// 生成文件Model
	faqFile := model.FaqFile{
		FaqId:          0,
		FileCode:       req.FileCode,
		Mode:           xs3.PrivateMode,
		Bucket:         xconfig.OSSConf.Bucket,
		Path:           fmt.Sprintf(OSSFaqFilePath, req.FileCode, uuidName),
		Uuid:           uuidStr,
		OriginFileName: req.File.Filename,
		FileName:       uuidName,
		FileType:       path.Ext(req.File.Filename),
		FileSize:       uint32(req.File.Size),
	}
	if err = db.Create(&faqFile).Error; err != nil {
		return resp, err
	}
	// 重置 reader 指針
	_, err = reader.Seek(0, io.SeekStart)
	if err != nil {
		return resp, err
	}
	// 最後再上傳源文件
	err = xs3.UploadObjectFromReader(faqFile.Bucket, faqFile.Path, faqFile.OriginFileName, reader)
	if err != nil {
		return resp, err
	}
	resp.FaqFileUuid = faqFile.Uuid
	return resp, nil
}

// 綁定常見問題ID
func (s *faqFileService) BindFaq(db *gorm.DB, faqId uint64, faqFileUuids []string) error {
	return db.Model(&model.FaqFile{}).
		Where("uuid in (?)", faqFileUuids).
		Where("faq_id = ?", 0).
		Update("faq_id", faqId).Error
}

type FaqFilePreviewUrlReq struct {
	FaqFileUuid string `form:"faqFileUuid" binding:"required"`
}

type FaqFilePreviewUrlResp struct {
	Url string `json:"url"`
}

// 生成常見問題文件預覽URL
func (s *faqFileService) GenPreviewUrlByUuid(db *gorm.DB, faqFileUuid string) (string, error) {
	var err error
	var m model.FaqFile
	if err = db.First(&m, "uuid = ?", faqFileUuid).Error; err != nil {
		return "", err
	}
	// 有效期24小時
	var url string
	url, err = xs3.GetPresignDownloadUrl(m.Bucket, m.Path, 24*time.Hour)
	if err != nil {
		return "", err
	}
	return url, nil
}
