package services

import (
	"strings"
)

var keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
var keys = strings.Split(keyStr, "")
var long = len(keyStr)

var FrontendEncryptService = new(frontendEncryptService)

type frontendEncryptService struct{}

// Encrypt 把字串轉成自定義編碼
func (s *frontendEncryptService) Encrypt(value string) string {
	var v strings.Builder
	for _, r := range value {
		cat := int(r)
		cat1 := cat % long
		cat = (cat - cat1) / long
		cat2 := cat % long
		cat = (cat - cat2) / long
		cat3 := cat % long
		v.WriteString(keys[cat3] + keys[cat2] + keys[cat1])
	}
	return v.String()
}

// Decrypt 把編碼轉回原字串
func (s *frontendEncryptService) Decrypt(value string) string {
	alen := len(value) / 3
	arr := make([]rune, alen)
	num := 0
	for i := 0; i < alen; i++ {
		cat1 := strings.IndexRune(keyStr, rune(value[num]))
		num++
		cat2 := strings.IndexRune(keyStr, rune(value[num]))
		num++
		cat3 := strings.IndexRune(keyStr, rune(value[num]))
		num++
		arr[i] = rune(cat1*long*long + cat2*long + cat3)
	}
	return string(arr)
}
