package services

import (
	"fmt"
	"strings"
	"time"

	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgoogle"
	"github.com/Norray/xrocket/xredis"
	uuid "github.com/satori/go.uuid"
)

const (
	cacheKeyGoogleOAuthState = "cache:google_oauth_state:%s"
)

type googleOAuthService struct{}

var GoogleOAuthService = new(googleOAuthService)

type GoogleOAuthCache struct {
	ClientIp string `json:"clientIp"`
}

// GenerateAuthURL generates the Google OAuth authorization URL
func (s *googleOAuthService) GenerateAuthURL(nc xapp.NGinCtx) (string, error) {
	var err error
	stateToken := strings.ToLower(uuid.NewV4().String() + "-" + uuid.NewV4().String())
	cacheKey := fmt.Sprintf(cacheKeyGoogleOAuthState, stateToken)
	// 保存 ip 信息 至 redis
	err = xredis.SetStruct(nc.C, cacheKey, GoogleOAuthCache{
		ClientIp: nc.C.ClientIP(),
	}, 10*time.Minute)
	if err != nil {
		return "", err
	}
	authURL, err := xgoogle.GetUpdateCodeWebLink(stateToken)
	if err != nil {
		return "", err
	}
	return authURL, nil
}

type CallbackReq struct {
	State string `form:"state" json:"state" binding:"required"`
	Code  string `form:"code" json:"code" binding:"required"`
}

func (s *googleOAuthService) GetUserProfile(nc xapp.NGinCtx, req CallbackReq) (GoogleOAuthCache, xgoogle.UserInfo, error) {
	cacheKey := fmt.Sprintf(cacheKeyGoogleOAuthState, req.State)
	var cache GoogleOAuthCache
	exist, err := xredis.GetStruct(nc.C, cacheKey, &cache)
	if err != nil {
		return GoogleOAuthCache{}, xgoogle.UserInfo{}, err
	}
	if !exist {
		return GoogleOAuthCache{}, xgoogle.UserInfo{}, fmt.Errorf("state token not exist")
	}
	err = xredis.DeleteKey(nc.C, cacheKey)
	if err != nil {
		return GoogleOAuthCache{}, xgoogle.UserInfo{}, err
	}
	if cache.ClientIp != nc.C.ClientIP() {
		return GoogleOAuthCache{}, xgoogle.UserInfo{}, fmt.Errorf("client ip not match")
	}
	// 交換token
	token, err := xgoogle.ExchangeCodeForToken(nc.C, req.Code)
	if err != nil {
		return GoogleOAuthCache{}, xgoogle.UserInfo{}, err
	}
	profile, err := xgoogle.GetUserProfile(token)
	if err != nil {
		return GoogleOAuthCache{}, xgoogle.UserInfo{}, err
	}
	return cache, profile, nil
}
