package services

import (
	"github.com/Norray/medic-crew/model"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type jobAllowanceService struct{}

var JobAllowanceService = new(jobAllowanceService)

// GetAllowance 獲取津貼配置
func (s *jobAllowanceService) GetAllowance(db *gorm.DB, jobId uint64) ([]model.JobAllowance, error) {
	var jobAllowances []model.JobAllowance
	builder := db.Model(&model.JobAllowance{}).
		Where("job_id = ?", jobId).
		Order("job_id ASC").
		Order("allowance_id ASC")

	if err := builder.Find(&jobAllowances).Error; err != nil {
		return nil, err
	}
	return jobAllowances, nil
}

// CalculateJobShiftAllowanceDetails 計算特定 JobShift 的津貼明細
func (s *jobAllowanceService) CalculateJobShiftAllowanceDetails(db *gorm.DB, jobShiftId uint64) (decimal.Decimal, []model.AllowanceDetail, error) {
	// 獲取 JobShift 信息
	var jobShift model.JobShift
	if err := db.First(&jobShift, jobShiftId).Error; err != nil {
		return decimal.Zero, nil, err
	}

	// 獲取該 Job 的津貼配置
	jobAllowances, err := s.GetAllowance(db, jobShift.JobId)
	if err != nil {
		return decimal.Zero, nil, err
	}

	// 獲取該 Job 的所有 JobShift（用於 JOB 類型津貼計算）
	var allJobShifts []model.JobShift
	if err := db.Where("job_id = ?", jobShift.JobId).Find(&allJobShifts).Error; err != nil {
		return decimal.Zero, nil, err
	}
	totalShifts := len(allJobShifts)

	totalAmount := decimal.Zero
	var details []model.AllowanceDetail

	for _, allowance := range jobAllowances {
		var amount decimal.Decimal

		switch allowance.AllowanceType {
		case model.JobAllowanceAllowanceTypeHourly:
			// 按小時：基礎金額 × 該班次工時
			amount = allowance.BaseAmount.Mul(jobShift.PayHours)

		case model.JobAllowanceAllowanceTypeShift:
			// 按班次：固定金額
			amount = allowance.BaseAmount

		case model.JobAllowanceAllowanceTypeJob:
			// 按職位：總金額 ÷ 班次數
			if totalShifts > 0 {
				amount = allowance.BaseAmount.Div(decimal.NewFromInt(int64(totalShifts)))

				// 處理誤差：如果是最後一個班次，加上剩餘誤差
				if s.isLastJobShift(jobShiftId, allJobShifts) {
					averageAmount := allowance.BaseAmount.Div(decimal.NewFromInt(int64(totalShifts))).Round(2)
					totalDistributed := averageAmount.Mul(decimal.NewFromInt(int64(totalShifts)))
					difference := allowance.BaseAmount.Sub(totalDistributed)
					amount = amount.Add(difference)
				}
			}
		}

		// 處理退休金
		superAmount := decimal.Zero
		if allowance.AttractsSuperannuation == "Y" {
			superAmount = amount.Mul(decimal.NewFromFloat(0.12)).Round(2)
		}

		amount = amount.Round(2)
		totalAmount = totalAmount.Add(amount)

		details = append(details, model.AllowanceDetail{
			AllowanceId:            allowance.AllowanceId,
			AllowanceName:          allowance.AllowanceName,
			AllowanceType:          allowance.AllowanceType,
			BaseAmount:             allowance.BaseAmount,
			Amount:                 amount,
			AttractsSuperannuation: allowance.AttractsSuperannuation,
			SuperAmount:            superAmount,
		})
	}

	return totalAmount.Round(2), details, nil
}

// 判斷是否為最後一個 JobShift（用於誤差處理）
func (s *jobAllowanceService) isLastJobShift(jobShiftId uint64, allJobShifts []model.JobShift) bool {
	if len(allJobShifts) == 0 {
		return false
	}

	var lastShift *model.JobShift
	for i := range allJobShifts {
		if lastShift == nil || allJobShifts[i].BeginTime.After(*lastShift.BeginTime) {
			lastShift = &allJobShifts[i]
		}
	}

	return jobShiftId == lastShift.Id
}
