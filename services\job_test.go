package services

import (
	"fmt"
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
)

func TestJobService_CalculateScore(t *testing.T) {
	xconfig.Setup("../config/app.ini")
	xgorm.DefaultSetup()
	jobId := 96
	professionalId := 38

	var job model.Job
	if err := xgorm.DB.First(&job, jobId).Error; err != nil {
		t.Fatalf("Failed to get job: %v", err)
	}
	var professional model.Professional
	if err := xgorm.DB.First(&professional, professionalId).Error; err != nil {
		t.Fatalf("Failed to get professional: %v", err)
	}
	score, err := JobScoreService.CalculateScore(xgorm.DB, job, professional)
	if err != nil {
		t.Fatalf("Failed to calculate score: %v", err)
	}
	fmt.Println(score)
}

// TestUpdateJobShiftTimeAndWeekdayTypeFromJobShifts 從 JobShift 表更新 Job 的 ShiftTimeType 和 WeekdayType 字段
func TestUpdateJobShiftTimeAndWeekdayTypeFromJobShifts(t *testing.T) {
	// 初始化配置和數據庫連接
	xconfig.Setup("../config/app.ini")
	xgorm.DefaultSetup()

	// 查詢所有需要更新的 Job 記錄
	var jobs []model.Job
	if err := xgorm.DB.
		//Where("shift_time_type = '' OR weekday_type = ''").
		//Where("id = ?", 183).
		Find(&jobs).Error; err != nil {
		t.Fatalf("Failed to get jobs: %v", err)
	}

	fmt.Printf("找到 %d 個需要更新的工作記錄\n", len(jobs))

	// 統計更新數量
	updateCount := 0

	// 開始事務
	tx := xgorm.DB.Begin()

	// 遍歷所有 Job 記錄
	for _, job := range jobs {
		// 使用 JobService.LoadJobShiftItems 函數獲取班次信息
		jobShiftItems, err := JobService.LoadJobShiftItems(tx, job.FacilityId, job.Id)
		if err != nil {
			t.Logf("Failed to load job shift items for job %d: %v", job.Id, err)
			continue
		}

		if len(jobShiftItems) == 0 {
			t.Logf("No job shifts found for job %d", job.Id)
			continue
		}
		var serviceLocation model.ServiceLocation
		if err := xgorm.DB.First(&serviceLocation, job.FacilityId).Error; err != nil {
			t.Fatalf("Failed to get service location: %v", err)
		}

		// 使用 JobService.GenerateShiftTimeAndWeekdays 函數生成值
		shiftTimeType, weekdayType, err := JobService.GenerateShiftTimeAndWeekdays(jobShiftItems, serviceLocation.Timezone)
		if err != nil {
			t.Logf("Failed to generate shift time and weekday type for job %d: %v", job.Id, err)
			continue
		}

		// 更新字段
		updateMap := map[string]interface{}{
			"shift_time_type": shiftTimeType,
			"weekday_type":    weekdayType,
		}

		// 保存記錄
		if err := tx.Model(&job).Where("id = ?", job.Id).Updates(updateMap).Error; err != nil {
			t.Logf("Failed to update job %d: %v", job.Id, err)
			continue
		}
		updateCount++
	}

	// 提交事務
	if err := tx.Commit().Error; err != nil {
		t.Fatalf("Failed to commit transaction: %v", err)
	}

	fmt.Printf("成功更新了 %d 個工作記錄\n", updateCount)
}
