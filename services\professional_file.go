package services

import (
	"fmt"
	"io"
	"mime/multipart"
	"path/filepath"
	"strings"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xs3"
	"github.com/Norray/xrocket/xtool"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

var ProfessionalFileService = new(professionalFileService)

type professionalFileService struct{}

// region ---------------------------------------------------- Checker ----------------------------------------------------

// 檢查文件是否存在
func (s *professionalFileService) CheckFileExist(db *gorm.DB, userId uint64, ids []uint64, fileCodes ...[]string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.professional_file.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	if len(ids) == 0 {
		return true, i18n.Message{}, nil
	}
	ids = xtool.Uint64ArrayDeduplication(ids)

	for _, id := range ids {
		if id == 0 {
			return false, msg, nil
		}
	}

	var count int64
	builder := db.Model(&model.ProfessionalFile{}).
		Where("user_id = ?", userId).
		Where("id IN (?)", ids)
	if len(fileCodes) > 0 {
		builder = builder.Where("file_code IN (?)", fileCodes[0])
	}
	if err := builder.Count(&count).Error; err != nil {
		return false, msg, err
	}
	if int64(len(ids)) == count {
		return true, i18n.Message{}, nil
	}
	return false, msg, nil
}

// 檢查機構是否可以訪問專業人士的文件
func (s *professionalFileService) FacilityCheckFileExist(db *gorm.DB, facilityId uint64, professionalId uint64, id uint64, reqUserId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.professional_file.id.facility.does_not_exist",
		Other: "No such file, please try again later.",
	}
	var professionalFile model.ProfessionalFile
	var professional model.Professional // 僅包含id,user_id
	// 檢查專業人士是否存在
	if err := db.Select("id,user_id").Where("id = ?", professionalId).First(&professional).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	} else if err != nil {
		return false, msg, nil
	}
	// 檢查文件是否存在
	if err := db.Where("user_id = ?", professional.UserId).Where("id = ?", id).First(&professionalFile).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	} else if err != nil {
		return false, msg, nil
	}

	if canAccess, exist := FacilityAccessProfessionalFileTypeMap[professionalFile.FileCode]; !canAccess || !exist {
		return false, msg, nil
	}

	// 如果是圖片則允許 其他文件必須存在於專業人士的申請記錄中
	if professionalFile.FileCode == model.ProfessionalFileCodePhoto {
		return true, i18n.Message{}, nil
	}
	var jobApplication model.JobApplication
	builder := db.Table("job_application AS ja").
		Select("ja.id").
		Joins("JOIN professional_file_relation AS pfr ON pfr.professional_id = ja.professional_id").
		Joins("JOIN professional_file AS pf ON pfr.professional_file_id = pf.id AND pf.user_id = ja.user_id").
		Where("ja.facility_id = ?", facilityId).
		Where("ja.user_id = ?", professional.UserId).
		Where("pf.id = ?", id).
		Where("pf.file_code <> ?", model.ProfessionalFileCodePhoto).
		Where("ja.professional_id = ?", professional.Id)
	err := FacilityUserDepartmentService.GetCanAccessDepartmentByUserId(db, reqUserId, func(departments []model.Department) {
		builder = builder.Joins("JOIN job_department AS jd ON jd.job_id = ja.job_id")
		builder = builder.Scopes(FacilityUserDepartmentService.FilterByCanAccessDepartment("jd.department_id", departments))
	})
	if err != nil {
		return false, msg, err
	}
	if err := builder.First(&jobApplication).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	} else if err != nil {
		return false, msg, nil
	}
	return true, i18n.Message{}, nil
}

func (s *professionalFileService) CheckFileExistByUuid(db *gorm.DB, userId uint64, fileCodes []string, uuids []string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.professional_file.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	if len(uuids) == 0 {
		return true, i18n.Message{}, nil
	}
	uuids = xtool.StringArrayDeduplication(uuids)

	for _, id := range uuids {
		if id == "" {
			return false, msg, nil
		}
	}

	var count int64
	if err := db.Model(&model.ProfessionalFile{}).Where("user_id = ?", userId).
		Where("file_code IN (?)", fileCodes).
		Where("uuid IN (?)", uuids).Count(&count).Error; err != nil {
		return false, msg, err
	}
	if int64(len(uuids)) == count {
		return true, i18n.Message{}, nil
	}
	return false, msg, nil
}

// 檢查文件大小是否符合限制
func (s *professionalFileService) CheckFileSize(fileCode string, fileSize int64) (bool, i18n.Message, map[string]string, error) {
	limitations := model.ProfessionalFile{}.FileSizeLimitations()
	maxSize, exists := limitations[fileCode]
	if !exists {
		// FileCode 不存在於限制列表中
		return false, model.MsgFileCodeNotExist, nil, nil
	}

	if fileSize > maxSize {
		templateData := map[string]string{
			"MaxSize": fmt.Sprintf("%d", maxSize/(1024*1024)),
		}
		return false, model.MsgFileSizeTooLarge, templateData, nil
	}
	return true, i18n.Message{}, nil, nil
}

// 檢查文件類型是否允許
func (s *professionalFileService) CheckFileType(fileCode string, fileType string) (bool, i18n.Message, map[string]string, error) {
	allowedTypes := model.ProfessionalFile{}.AllowedFileTypes()
	types, exists := allowedTypes[fileCode]
	if !exists {
		// FileCode 不存在於允許類型列表中
		return false, model.MsgFileCodeNotExist, nil, nil
	}

	// 轉換為小寫進行比較
	fileTypeLower := strings.ToLower(fileType)
	for _, allowedType := range types {
		if fileTypeLower == allowedType {
			return true, i18n.Message{}, nil, nil
		}
	}

	templateData := map[string]string{
		"FileType":     fileType,
		"AllowedTypes": strings.Join(types, ", "),
	}
	return false, model.MsgFileTypeNotAllowed, templateData, nil
}

// endregion ---------------------------------------------------- Checker ----------------------------------------------------

// region ---------------------------------------------------- 上傳文件 ----------------------------------------------------

type ProfessionalFileUploadFileReq struct {
	FileCode        string                `form:"fileCode" binding:"required"` // 文件類型 由CheckFileType校驗FileCoe
	ProfessionalId  uint64                `form:"-" swaggerignore:"true"`
	File            *multipart.FileHeader `form:"-" swaggerignore:"true"`
	AiDateAndNumber AiDateAndNumber       `form:"-" swaggerignore:"true"`
}
type ProfessionalFileUploadFileResp struct {
	ProfessionalFileId uint64 `json:"professionalFileId"` // 專業人士文件ID
	ExpireDate         string `json:"expireDate"`         // 到期日
	IssueDate          string `json:"issueDate"`          // 發行日期
	Number             string `json:"number"`             // 識別號碼
}

// 0=ExpireDate 1=IssueDate
var ProfessionalAiFileMap = map[string]bool{
	model.ProfessionalFileCodeAhpraCertificate:              true,
	model.ProfessionalFileCodeIndemnityInsuranceCertificate: true,

	model.ProfessionalFileCodeAustralianPassport:                    true,
	model.ProfessionalFileCodeForeignPassport:                       true,
	model.ProfessionalFileCodeAustralianBirthCertificate:            true,
	model.ProfessionalFileCodeAustralianCitizenshipCertificate:      true,
	model.ProfessionalFileCodeCurrentAustraliaDriverLicence:         true,
	model.ProfessionalFileCodeAustralianPublicServiceEmployeeIDCard: true,
	model.ProfessionalFileCodeOtherAustralianGovernmentIssueIDCard:  true,
	model.ProfessionalFileCodeTertiaryStudentIDCard:                 true,
	model.ProfessionalFileCodeCreditDebitAtmCard:                    true,
	model.ProfessionalFileCodeMedicareCard:                          true,
	model.ProfessionalFileCodeUtilityBillOrRateNotice:               true,
	model.ProfessionalFileCodeStatementFromFinancialInstitution:     true,
	model.ProfessionalFileCodeCentrelinkOrPensionCard:               true,

	model.ProfessionalFileCodeVisa:                                  true,
	model.ProfessionalFileCodeNationalCriminalCheck:                 true,
	model.ProfessionalFileCodeWorkingWithChildrenOrVulnerablePeople: true,

	model.ProfessionalFileCodeAdditionalCertification: true,
}

func (s *professionalFileService) UploadFile(db *gorm.DB, req ProfessionalFileUploadFileReq, userId uint64) (ProfessionalFileUploadFileResp, error) {
	var err error
	var resp ProfessionalFileUploadFileResp
	var professional model.Professional
	if err = db.Where("id = ?", req.ProfessionalId).First(&professional).Error; err != nil {
		return resp, err
	}

	filename := req.File.Filename
	uuidStr := uuid.NewV4().String()
	ext := filepath.Ext(filename) // 獲取文件擴展名
	uuidFileName := uuidStr + ext

	// 讀取文件
	reader, err := req.File.Open()
	if err != nil {
		return resp, err
	}
	defer reader.Close()

	// 構建縮略圖
	thumbnailResp, err := ImageService.ConvertToThumbnail(reader, filename, uuidStr)
	if err != nil {
		return resp, err
	}

	// 上傳原文件到OSS
	professionalFile := model.ProfessionalFile{
		UserId:             userId,
		FileCode:           req.FileCode,
		Mode:               xs3.PrivateMode,
		Bucket:             xconfig.OSSConf.Bucket,
		Path:               fmt.Sprintf(OSSProfessionalFilePath, professional.Id, req.FileCode, uuidFileName),
		Uuid:               uuidStr,
		OriginFileName:     filename,
		FileName:           uuidFileName,
		FileType:           ext,
		FileSize:           uint32(req.File.Size),
		ThumbnailPath:      fmt.Sprintf(OSSProfessionalFilePath, professional.Id, req.FileCode, thumbnailResp.ThumbnailUuidName),
		ThumbnailFileSize:  thumbnailResp.Size,
		AiResultJson:       req.AiDateAndNumber.Output,
		AiModel:            req.AiDateAndNumber.Model,
		AiInputTokenUsage:  req.AiDateAndNumber.InputToken,
		AiOutputTokenUsage: req.AiDateAndNumber.OutputToken,
	}
	// 先上傳縮略圖
	err = xs3.UploadObjectFromReader(professionalFile.Bucket, professionalFile.ThumbnailPath, professionalFile.OriginFileName, thumbnailResp.ThumbnailBody)
	if err != nil {
		return resp, err
	}
	// 重置 reader 指針
	_, err = reader.Seek(0, io.SeekStart)
	if err != nil {
		return resp, err
	}

	// 上傳原文件到OSS
	err = xs3.UploadObjectFromReader(professionalFile.Bucket, professionalFile.Path, professionalFile.OriginFileName, reader)
	if err != nil {
		return resp, err
	}
	if err = db.Create(&professionalFile).Error; err != nil {
		return resp, err
	}
	resp.ProfessionalFileId = professionalFile.Id
	resp.ExpireDate = req.AiDateAndNumber.ExpireDate
	resp.Number = req.AiDateAndNumber.IssueDate
	resp.Number = req.AiDateAndNumber.Number
	return resp, nil
}

// endregion ---------------------------------------------------- 上傳文件 ----------------------------------------------------

// region ---------------------------------------------------- 預覽文件 ----------------------------------------------------

type ProfessionalFileGetPreviewReq struct {
	ProfessionalFileId uint64 `form:"professionalFileId" binding:"required"`
	Thumb              string `form:"thumb" binding:"required,oneof=Y N"`
}

type ProfessionalFileGetPreviewByIdOrUuidReq struct {
	ProfessionalFileId   uint64 `form:"professionalFileId"`
	ProfessionalFileUuid string `form:"professionalFileUuid"`
	Thumb                string `form:"thumb" binding:"required,oneof=Y N"`
}

type ProfessionalFileGetPreviewResp struct {
	FileBytes []byte
	Filename  string
	UuidName  string
	FileUuid  string
}

func (s *professionalFileService) Preview(db *gorm.DB, req ProfessionalFileGetPreviewReq) (ProfessionalFileGetPreviewResp, error) {
	var err error
	var resp ProfessionalFileGetPreviewResp
	var m model.ProfessionalFile
	if err = db.
		First(&m, req.ProfessionalFileId).Error; err != nil {
		return resp, err
	}
	var object []byte
	if req.Thumb == "Y" {
		object, err = xs3.GetObject(m.Bucket, m.ThumbnailPath)
	} else {
		object, err = xs3.GetObject(m.Bucket, m.Path)
	}
	if err != nil {
		return ProfessionalFileGetPreviewResp{}, err
	}
	resp.FileBytes = object
	resp.Filename = m.OriginFileName
	resp.UuidName = m.FileName
	resp.FileUuid = m.Uuid
	return resp, nil
}

func (s *professionalFileService) PreviewByIdOrUuid(db *gorm.DB, req ProfessionalFileGetPreviewByIdOrUuidReq) (ProfessionalFileGetPreviewResp, error) {
	var err error
	var resp ProfessionalFileGetPreviewResp
	var m model.ProfessionalFile
	if req.ProfessionalFileUuid != "" {
		if err = db.Where("uuid = ?", req.ProfessionalFileUuid).First(&m).Error; err != nil {
			return resp, err
		}
	} else {
		if err = db.Where("id = ?", req.ProfessionalFileId).First(&m).Error; err != nil {
			return resp, err
		}
	}

	var object []byte
	if req.Thumb == "Y" {
		object, err = xs3.GetObject(m.Bucket, m.ThumbnailPath)
	} else {
		object, err = xs3.GetObject(m.Bucket, m.Path)
	}
	if err != nil {
		return ProfessionalFileGetPreviewResp{}, err
	}
	resp.FileBytes = object
	resp.Filename = m.OriginFileName
	resp.UuidName = m.FileName
	resp.FileUuid = m.Uuid

	return resp, nil
}

// endregion ---------------------------------------------------- 預覽文件 ----------------------------------------------------

// region ---------------------------------------------------- 機構預覽專業人士文件 ----------------------------------------------------

type ProfessionalFileGetPreviewByFacilityReq struct {
	FacilityId         uint64 `form:"facilityId" binding:"required"`
	ProfessionalId     uint64 `form:"professionalId" binding:"required"`
	ProfessionalFileId uint64 `form:"professionalFileId" binding:"required"`
	Thumb              string `form:"thumb" binding:"required,oneof=Y N"`
}

// endregion ---------------------------------------------------- 機構預覽專業人士文件 ----------------------------------------------------
