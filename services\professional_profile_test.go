package services

import (
	"fmt"
	"os"
	"strings"
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
	"gorm.io/gorm"
)

func TestFixProfessionalProfileReference(t *testing.T) {
	err := os.Chdir("..")
	if err != nil {
		t.Fatalf("Failed to change directory: %v", err)
		return
	}
	xconfig.Setup("./config/app.ini")
	xgorm.DefaultSetup()
	var allProfessional []model.Professional
	xgorm.DB.Find(&allProfessional)
	for _, professional := range allProfessional {
		profile, err := professional.UnmarshalProfile(professional.ProfileJson)
		if err != nil {
			t.Fatalf("Failed to unmarshal profile: %v", err)
		}
		// 刪除2個以上的數據
		if len(profile.References) >= 2 {
			profile.References = profile.References[:2]
		} else if len(profile.References) == 1 {
			profile.References = append(profile.References, model.ProfessionalReference{})
		} else {
			profile.References = append(profile.References, model.ProfessionalReference{})
			profile.References = append(profile.References, model.ProfessionalReference{})
		}
		err = professional.MarshalProfile(profile)
		if err != nil {
			return
		}
		professional.ReferenceFormStatus = model.ProfessionalReferenceFormStatusFilled
		for _, reference := range profile.References {
			if reference.FormStatus == model.ProfessionalProfileReferencesFormStatusPending || reference.FormStatus == "" {
				professional.ReferenceFormStatus = model.ProfessionalReferenceFormStatusUnfilled
			}
		}
		xgorm.DB.Save(&professional)
	}
}
func TestFixProfessionalInvalidFile(t *testing.T) {
	err := os.Chdir("..")
	if err != nil {
		t.Fatalf("Failed to change directory: %v", err)
		return
	}
	xconfig.Setup("./config/app.ini")
	xgorm.DefaultSetup()
	var allProfessional []model.Professional
	xgorm.DB.Find(&allProfessional)
	// 失效的文件編號
	invalidFileCodeMap := map[string]bool{
		"WORKING_WITH_CHILDREN_CHECK":         true,
		"WORKING_WITH_VULNERABLE_PEOPLE_CARD": true,
	}
	successMap := map[uint64][]string{}
	failMap := map[uint64][]string{}

	defer func() {
		fmt.Println("success count:", len(successMap))
		for professionalId, invalidFileList := range successMap {
			fmt.Printf("success professionalId: %d, invalidFileList: %v\n", professionalId, invalidFileList)
		}
		fmt.Println("--------------------------------")
		fmt.Println("fail count:", len(failMap))
		for professionalId, invalidFileList := range failMap {
			fmt.Printf("fail professionalId: %d, invalidFileList: %v\n", professionalId, invalidFileList)
		}
	}()

	tx := xgorm.DB.Begin()
	for _, professional := range allProfessional {
		invalidFileList := make([]string, 0)
		profile, err := professional.UnmarshalProfile(professional.ProfileJson)
		if err != nil {
			failMap[professional.Id] = invalidFileList
			tx.Rollback()
			t.Fatalf("Failed to unmarshal profile: %v", err)
			return
		}
		hasInvalidFile := false
		// 倒序刪除
		for i := len(profile.Files) - 1; i >= 0; i-- {
			if invalidFileCodeMap[profile.Files[i].FileCode] {
				invalidFileList = append(invalidFileList, profile.Files[i].FileCode)
				profile.Files = append(profile.Files[:i], profile.Files[i+1:]...)
				hasInvalidFile = true
			}
		}
		if hasInvalidFile {
			err = professional.MarshalProfile(profile)
			if err != nil {
				failMap[professional.Id] = invalidFileList
				tx.Rollback()
				t.Fatalf("Failed to marshal profile: %v", err)
				return
			}
			tx.Save(&professional)
			successMap[professional.Id] = invalidFileList
		}
	}
	tx.Commit()
}

func TestFixProfessionalPreferredSpecialities(t *testing.T) {
	err := os.Chdir("..")
	if err != nil {
		t.Fatalf("Failed to change directory: %v", err)
		return
	}
	xconfig.Setup("./config/app.ini")
	xgorm.DefaultSetup()
	var allProfessional []model.Professional
	xgorm.DB.Find(&allProfessional)
	successMap := map[uint64][]string{}
	failMap := map[uint64][]string{}

	defer func() {
		fmt.Println("success count:", len(successMap))
		for professionalId, _ := range successMap {
			fmt.Printf("success professionalId: %d", professionalId)
		}
		fmt.Println("--------------------------------")
		fmt.Println("fail count:", len(failMap))
		for professionalId, _ := range failMap {
			fmt.Printf("fail professionalId: %d", professionalId)
		}
	}()

	tx := xgorm.DB.Begin()
	for _, professional := range allProfessional {
		invalidFileList := make([]string, 0)
		profile, err := professional.UnmarshalProfile(professional.ProfileJson)
		if err != nil {
			failMap[professional.Id] = invalidFileList
			tx.Rollback()
			t.Fatalf("Failed to unmarshal profile: %v", err)
			return
		}
		hasInvalid := false
		if profile.PreferredSpecialities == nil || len(profile.PreferredSpecialities) == 0 {
			hasInvalid = true
			profile.PreferredSpecialities = make([]model.ProfessionalPreferredSpeciality, 0)
		}

		if hasInvalid {
			err = professional.MarshalProfile(profile)
			if err != nil {
				failMap[professional.Id] = invalidFileList
				tx.Rollback()
				t.Fatalf("Failed to marshal profile: %v", err)
				return
			}
			tx.Save(&professional)
			successMap[professional.Id] = invalidFileList
		}
	}
	tx.Commit()
}

func TestFixProfessionalSuperannuation(t *testing.T) {
	err := os.Chdir("..")
	if err != nil {
		t.Fatalf("Failed to change directory: %v", err)
		return
	}
	type userInfo struct {
		UserId    uint64
		FirstName string
		LastName  string
	}
	xconfig.Setup("./config/app.ini")
	xgorm.DefaultSetup()
	var allUser []userInfo
	xgorm.DB.Table("user AS u").
		Joins("JOIN professional AS p ON p.user_id = u.id AND p.data_type = ?", model.ProfessionalDataTypeApproved).
		Joins("LEFT JOIN professional_superannuation AS ps ON u.id = ps.user_id").
		Select("u.id AS user_id, p.first_name, p.last_name").
		Where("u.user_type = ?", model.UserUserTypeProfessional).
		Where("ps.id IS NULL").
		Find(&allUser)
	successMap := map[uint64][]string{}
	failMap := map[uint64][]string{}

	defer func() {
		fmt.Println("success count:", len(successMap))
		for userId, _ := range successMap {
			fmt.Printf("success userId: %d", userId)
		}
		fmt.Println("--------------------------------")
		fmt.Println("fail count:", len(failMap))
		for userId, _ := range failMap {
			fmt.Printf("fail userId: %d", userId)
		}
	}()

	tx := xgorm.DB.Begin()
	for _, user := range allUser {
		invalidFileList := make([]string, 0)
		// 創建默認的養老金記錄
		superannuation := []model.ProfessionalSuperannuation{
			{
				UserId:               user.UserId,
				DataType:             model.ProfessionalSuperannuationDataTypeDraft,
				FullName:             user.FirstName + " " + user.LastName,
				DeclarationConfirmed: "N",
			},
			{
				UserId:               user.UserId,
				DataType:             model.ProfessionalSuperannuationDataTypeSubmitted,
				FullName:             user.FirstName + " " + user.LastName,
				DeclarationConfirmed: "N",
			},
		}
		if err = tx.CreateInBatches(superannuation, 2).Error; err != nil {
			failMap[user.UserId] = invalidFileList
			tx.Rollback()
			t.Fatalf("Failed to create superannuation: %v", err)
			return
		}
		successMap[user.UserId] = invalidFileList
	}
	tx.Commit()
}

// TestFixIdCheckFileTypes 修復專業人士 ID Check 文件類型
func TestFixIdCheckFileTypes(t *testing.T) {
	fmt.Println("=== 開始修復專業人士 ID Check 文件類型 ===")

	// 初始化配置和數據庫
	err := os.Chdir("..")
	if err != nil {
		t.Fatalf("Failed to change directory: %v", err)
	}
	xconfig.Setup("./config/app.ini")
	xgorm.DefaultSetup()

	db := xgorm.DB
	if db == nil {
		t.Fatal("Failed to connect to database")
	}

	// 查詢所有有 APPROVED 版本的用戶
	usersWithApproved, err := queryUsersWithApprovedProfessionals(db)
	if err != nil {
		t.Fatalf("Failed to query users with approved professionals: %v", err)
	}

	fmt.Printf("找到 %d 個有已通過版本的用戶\n", len(usersWithApproved))

	if len(usersWithApproved) == 0 {
		fmt.Println("沒有需要處理的用戶")
		return
	}

	// 處理每個用戶
	successCount := 0
	errorCount := 0

	for i, userInfo := range usersWithApproved {
		fmt.Printf("\n[%d/%d] 處理用戶 ID: %d\n", i+1, len(usersWithApproved), userInfo.UserId)

		err := processUserIdCheckFiles(db, userInfo)
		if err != nil {
			fmt.Printf("❌ 處理失敗: %v\n", err)
			errorCount++
		} else {
			fmt.Printf("✅ 處理成功\n")
			successCount++
		}
	}

	fmt.Printf("\n=== 修復完成 ===\n")
	fmt.Printf("成功: %d, 失敗: %d, 總計: %d\n", successCount, errorCount, len(usersWithApproved))
}

// UserProfessionalInfo 用戶專業人士信息
type UserProfessionalInfo struct {
	UserId              uint64 `json:"userId"`
	ApprovedId          uint64 `json:"approvedId"`
	DraftId             uint64 `json:"draftId"`
	HasDraft            bool   `json:"hasDraft"`
	ApprovedProfileJson string `json:"approvedProfileJson"`
	DraftProfileJson    string `json:"draftProfileJson"`
}

// queryUsersWithApprovedProfessionals 查詢有 APPROVED 版本的用戶
func queryUsersWithApprovedProfessionals(db *gorm.DB) ([]UserProfessionalInfo, error) {
	var results []UserProfessionalInfo

	// 查詢有 APPROVED 版本的用戶，並檢查是否有 DRAFT 版本
	builder := db.Table("professional AS approved").
		Joins("LEFT JOIN professional AS draft ON draft.user_id = approved.user_id AND draft.data_type = ?", model.ProfessionalDataTypeDraft).
		Select([]string{
			"approved.user_id",
			"approved.id as approved_id",
			"approved.profile_json as approved_profile_json",
			"COALESCE(draft.id, 0) as draft_id",
			"CASE WHEN draft.id IS NOT NULL THEN 1 ELSE 0 END as has_draft",
			"COALESCE(draft.profile_json, '') as draft_profile_json",
		}).
		Where("approved.data_type = ?", model.ProfessionalDataTypeApproved).
		Order("approved.user_id")
	userIds := []uint64{15}
	if len(userIds) > 0 {
		builder = builder.Where("approved.user_id IN (?)", userIds)
	}
	err := builder.Scan(&results).Error
	return results, err
}

// processUserIdCheckFiles 處理用戶的 ID Check 文件
func processUserIdCheckFiles(db *gorm.DB, userInfo UserProfessionalInfo) error {
	// 開始事務
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 解析 APPROVED 版本的 ProfileJson
	var approvedProfessional model.Professional
	err := tx.Where("id = ?", userInfo.ApprovedId).First(&approvedProfessional).Error
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to get approved professional: %v", err)
	}

	approvedProfile, err := approvedProfessional.UnmarshalProfile(approvedProfessional.ProfileJson)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to unmarshal approved profile: %v", err)
	}

	// 2. 處理 IdCheckFileTypes
	idCheckFileTypes := processIdCheckFileTypes(&approvedProfile)
	fmt.Printf("    處理後的 IdCheckFileTypes: %s\n", idCheckFileTypes)

	// 3. 更新 APPROVED 版本
	approvedProfile.IdCheckFileTypes = idCheckFileTypes
	err = approvedProfessional.MarshalProfile(approvedProfile)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to marshal approved profile: %v", err)
	}

	err = tx.Save(&approvedProfessional).Error
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to save approved professional: %v", err)
	}

	// 4. 處理 DRAFT 版本
	err = processDraftVersion(tx, userInfo.DraftId, idCheckFileTypes)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to process draft version: %v", err)
	}

	// 提交事務
	return tx.Commit().Error
}

// processIdCheckFileTypes 處理 IdCheckFileTypes 字段
func processIdCheckFileTypes(profile *model.ProfessionalProfile) string {
	// 第一種情況：沒有 IdCheckFileTypes 字段或等於空白
	if profile.IdCheckFileTypes == "" {
		fmt.Printf("    情況1: IdCheckFileTypes 為空，從 Files 中提取\n")

		// 收集 ID Check 文件類型
		var idCheckFileCodes []string
		var filesToRemove []int // 記錄要刪除的文件索引

		for i, file := range profile.Files {
			// 檢查是否為 ID Check 文件
			if _, isIdCheckFile := professionalProfileIdCheckFileMap[file.FileCode]; isIdCheckFile {
				// 檢查文件是否有上傳（有 ProfessionalFileIds）
				if len(file.ProfessionalFileIds) > 0 {
					idCheckFileCodes = append(idCheckFileCodes, file.FileCode)
					filesToRemove = append(filesToRemove, i)
					fmt.Printf("      找到 ID Check 文件: %s\n", file.FileCode)
				}
			}
		}

		// 去重
		idCheckFileCodes = removeDuplicateStrings(idCheckFileCodes)

		// 從 Files 中刪除 ID Check 文件（從後往前刪除，避免索引問題）
		for i := len(filesToRemove) - 1; i >= 0; i-- {
			index := filesToRemove[i]
			profile.Files = append(profile.Files[:index], profile.Files[index+1:]...)
		}

		// 設置 IdCheckFileTypes
		profile.IdCheckFileTypes = strings.Join(idCheckFileCodes, ",")
		fmt.Printf("      設置 IdCheckFileTypes: %s\n", profile.IdCheckFileTypes)

		return profile.IdCheckFileTypes
	} else {
		// 第二種情況：IdCheckFileTypes 有值
		fmt.Printf("    情況2: IdCheckFileTypes 已有值: %s\n", profile.IdCheckFileTypes)
		return profile.IdCheckFileTypes
	}
}

// processDraftVersion 處理 DRAFT 版本
func processDraftVersion(tx *gorm.DB, draftId uint64, idCheckFileTypes string) error {
	// 獲取 DRAFT 版本
	var draftProfessional model.Professional
	err := tx.Where("id = ?", draftId).First(&draftProfessional).Error
	if err != nil {
		return fmt.Errorf("failed to get draft professional: %v", err)
	}

	// 解析 DRAFT 版本的 ProfileJson
	draftProfile, err := draftProfessional.UnmarshalProfile(draftProfessional.ProfileJson)
	if err != nil {
		return fmt.Errorf("failed to unmarshal draft profile: %v", err)
	}

	// 複製 IdCheckFileTypes 到 DRAFT 版本
	draftProfile.IdCheckFileTypes = idCheckFileTypes
	fmt.Printf("    複製 IdCheckFileTypes 到 DRAFT: %s\n", idCheckFileTypes)

	// 從 DRAFT 版本的 Files 中刪除所有 ID Check 文件
	var updatedFiles []model.ProfessionalProfileFile
	removedCount := 0

	for _, file := range draftProfile.Files {
		// 如果不是 ID Check 文件，保留
		if _, isIdCheckFile := professionalProfileIdCheckFileMap[file.FileCode]; !isIdCheckFile {
			updatedFiles = append(updatedFiles, file)
		} else {
			removedCount++
			fmt.Printf("      從 DRAFT 中刪除 ID Check 文件: %s\n", file.FileCode)
		}
	}

	draftProfile.Files = updatedFiles
	fmt.Printf("    從 DRAFT 中刪除了 %d 個 ID Check 文件\n", removedCount)

	// 更新 DRAFT 版本
	err = draftProfessional.MarshalProfile(draftProfile)
	if err != nil {
		return fmt.Errorf("failed to marshal draft profile: %v", err)
	}

	err = tx.Save(&draftProfessional).Error
	if err != nil {
		return fmt.Errorf("failed to save draft professional: %v", err)
	}

	return nil
}

// removeDuplicateStrings 去除字符串切片中的重複項
func removeDuplicateStrings(slice []string) []string {
	keys := make(map[string]bool)
	var result []string

	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}

	return result
}
