package services

import (
	"os"
	"testing"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xtool"
)

func TestCreateSuperAdmin(t *testing.T) {
	err := os.Chdir("..")
	if err != nil {
		t.Fatalf("Failed to change directory: %v", err)
		return
	}
	xconfig.Setup("./config/app.ini")
	xgorm.DefaultSetup()
	db := xgorm.DB
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 檢查是否已存在超級管理員
	var oldSuperAdmin xmodel.User
	if err = db.Where("user_type = ?", model.UserUserTypeSuperAdmin).First(&oldSuperAdmin).Error; xgorm.IsSqlErr(err) {
		t.Fatalf("Super admin already exists: %v", err)
		return
	}
	if oldSuperAdmin.Id > 0 {
		t.Fatalf("Super admin already exists: %v", oldSuperAdmin)
		return
	}
	email := "<EMAIL>"  // 填寫超級管理員電郵
	password := "sul!fnTo2.b/2025" // 填寫超級管理員密碼

	if password == "" {
		t.Fatalf("Super admin password is empty")
		return
	}
	// 創建超級管理員
	user := xmodel.User{
		Username:            email,
		Password:            xtool.EncodeStringWithSalt(xtool.FrontendEncodeStringWithSalt(password, "medic-crew"), xconfig.AppConf.PasswordSalt),
		OAuthPlatform:       "",
		OAuthId:             "",
		Email:               email,
		Name:                "SuperAdmin",
		RegionNo:            "",
		Phone:               "",
		LogoBackgroundColor: "",
		UserType:            model.UserUserTypeSuperAdmin,
		Status:              xmodel.UserStatusEnable,
	}
	if err = tx.Create(&user).Error; err != nil {
		t.Fatalf("Failed to create super admin: %v", err)
		return
	}
	// 關聯角色
	var role xmodel.Role
	if err = db.Model(&role).Where("user_type = ?", model.UserUserTypeSuperAdmin).First(&role).Error; err != nil {
		t.Fatalf("Failed to get role: %v", err)
		return
	}
	userRole := xmodel.UserRole{
		UserId: user.Id,
		RoleId: role.Id,
	}
	if err = tx.Create(&userRole).Error; err != nil {
		t.Fatalf("Failed to create user role: %v", err)
		return
	}
	if err = tx.Commit().Error; err != nil {
		t.Fatalf("Failed to commit transaction: %v", err)
		return
	}
}
